import http from "@cm/alova";

/**
 * 获取验证码请求
 * 请求地址: /blade-auth/oauth/captcha
 * @params {string}  tenantId 租户id
 * @returns {Promise}  返回请求结果
 */
export function getCaptcha() {
  return http.get("/blade-auth/oauth/captcha");
}

/**
 * @description 上传文件
 * @param {File} file 文件
 * @returns {Promise<any>} 返回文件信息
 */
export function uploadFileAPI(file, name) {
  const formData = new FormData();
  formData.append(name, file);
  return http.post("/upload", formData);
}
