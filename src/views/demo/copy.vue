<script setup>
import { closeToast, showLoadingToast, showToast } from 'vant'
import { loginByUsername } from '@/api/user'

definePage({
  name: 'Copy',
  meta: {
    title: '复制',
    navBar: false,
    isAuth: false,
  },
})

const router = useRouter()

// 表单数据
const formData = reactive({
  username: '',
  password: '',
})

// 表单状态
const formState = reactive({
  showPassword: false,
  rememberPassword: false,
  isLoading: false,
  errors: {
    username: '',
    password: '',
  },
})

// 动画状态
const animationState = reactive({
  pageLoaded: false,
  formVisible: false,
})

// 页面加载动画
onMounted(() => {
  setTimeout(() => {
    animationState.pageLoaded = true
  }, 100)

  setTimeout(() => {
    animationState.formVisible = true
  }, 300)
})

// Vant 表单验证规则
const usernameRules = [
  { required: true, message: '请输入用户名或邮箱' },
  {
    validator: (value) => {
      if (value.includes('@')) {
        const emailRegex = /^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/
        return emailRegex.test(value) || '请输入正确的邮箱格式'
      }
      return value.length >= 3 || '用户名至少3个字符'
    },
  },
]

const passwordRules = [
  { required: true, message: '请输入密码' },
  { min: 6, message: '密码至少6个字符' },
]

// 表单引用
const formRef = ref()

// 登录提交 - 使用 Vant 表单验证
async function handleLogin() {
  try {
    // 使用 Vant 表单验证
    await formRef.value.validate()
  }
  catch {
    // 验证失败，直接返回
    return
  }

  formState.isLoading = true
  showLoadingToast({
    message: '登录中...',
    forbidClick: true,
    duration: 0,
  })

  try {
    await loginByUsername(formData.username, formData.password)

    closeToast()
    showToast({
      type: 'success',
      message: '登录成功',
      duration: 1500,
    })

    setTimeout(() => {
      router.push('/')
    }, 1500)
  }
  catch (error) {
    closeToast()
    showToast({
      type: 'fail',
      message: error.message || '登录失败，请重试',
      duration: 2000,
    })
  }
  finally {
    formState.isLoading = false
  }
}

// 切换密码显示状态
function togglePasswordVisibility() {
  formState.showPassword = !formState.showPassword
}

// 忘记密码
function handleForgotPassword() {
  showToast('忘记密码功能开发中...')
}
</script>

<template>
  <div class="login-page">
    <!-- 登录容器 -->
    <div class="login-container">
      <!-- 头部区域 -->
      <div class="login-header" :class="{ 'header-visible': animationState.pageLoaded }">
        <div class="logo-container">
          <div class="logo-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round" />
              <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round" />
              <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round" />
            </svg>
          </div>
        </div>
        <h1 class="login-title">
          欢迎回来
        </h1>
        <p class="login-subtitle">
          请登录您的账户以继续
        </p>
      </div>

      <!-- 登录表单 -->
      <div class="login-form-container" :class="{ 'form-visible': animationState.formVisible }">
        <van-form ref="formRef" class="login-form" @submit="handleLogin">
          <!-- 用户名输入框 -->
          <div class="input-group">
            <van-field
              v-model="formData.username"
              name="username"
              placeholder="用户名或邮箱"
              :rules="usernameRules"
              class="custom-field"
            >
              <template #left-icon>
                <div class="input-icon">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
                </div>
              </template>
            </van-field>
          </div>

          <!-- 密码输入框 -->
          <div class="input-group">
            <van-field
              v-model="formData.password"
              :type="formState.showPassword ? 'text' : 'password'"
              name="password"
              placeholder="密码"
              :rules="passwordRules"
              class="custom-field"
            >
              <template #left-icon>
                <div class="input-icon">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
                </div>
              </template>
              <template #right-icon>
                <button
                  type="button"
                  class="password-toggle"
                  @click="togglePasswordVisibility"
                >
                  <svg v-if="formState.showPassword" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.231 7.81663 6.62 6.68" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1749 15.0074 10.8016 14.8565C10.4283 14.7056 10.0887 14.4811 9.80385 14.1962C9.51900 13.9113 9.29449 13.5717 9.14359 13.1984C8.99269 12.8251 8.91855 12.4247 8.92563 12.0219C8.93271 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4859 9.58525 10.1546 9.88 9.88" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
                  <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
                </button>
              </template>
            </van-field>
          </div>

          <!-- 记住密码和忘记密码 -->
          <div class="form-options">
            <van-checkbox
              v-model="formState.rememberPassword"
              class="custom-checkbox"
            >
              记住密码
            </van-checkbox>
            <button
              type="button"
              class="forgot-password"
              @click="handleForgotPassword"
            >
              忘记密码？
            </button>
          </div>

          <!-- 登录按钮 -->
          <van-button
            type="primary"
            native-type="submit"
            block
            class="login-button"
            :loading="formState.isLoading"
            loading-text="登录中..."
          >
            登录
          </van-button>
        </van-form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// 页面整体样式
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.8s ease-in-out;
}

// 背景装饰
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;

  .bg-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;

    &.bg-circle-1 {
      width: 400px; // 200px * 2 = 400px，适配750px基准
      height: 400px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    &.bg-circle-2 {
      width: 300px; // 150px * 2 = 300px
      height: 300px;
      top: 60%;
      right: 15%;
      animation-delay: 2s;
    }

    &.bg-circle-3 {
      width: 200px; // 100px * 2 = 200px
      height: 200px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-40px) rotate(180deg); // 20px * 2 = 40px，适配750px基准
  }
}

// 登录容器 - 750px 设计基准优化
.login-container {
  position: relative;
  z-index: 2;
  width: 690px; // 750px * 0.92 = 690px，保持合适的边距
  max-width: 690px;
  padding: 80px 48px; // 增大内边距，提供更好的视觉空间
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(40px); // 增强毛玻璃效果
  border-radius: 48px; // 增大圆角，更现代化
  box-shadow: 0 40px 80px rgba(0, 0, 0, 0.1); // 增强阴影效果
  margin: 32px auto; // 增大外边距
}

// 头部区域 - 750px 设计基准优化
.login-header {
  text-align: center;
  margin-bottom: 88px; // 增大底部间距
  opacity: 0;
  transform: translateY(60px); // 增大动画位移
  transition: all 0.6s ease-out;

  &.header-visible {
    opacity: 1;
    transform: translateY(0);
  }

  .logo-container {
    margin-bottom: 64px; // 增大底部间距

    .logo-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 176px; // 88px * 2 = 176px，适配750px基准
      height: 176px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 44px; // 22px * 2 = 44px
      box-shadow: 0 24px 64px rgba(102, 126, 234, 0.3); // 增强阴影
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }

      svg {
        width: 88px; // 44px * 2 = 88px
        height: 88px;
        color: white;
      }
    }
  }

  .login-title {
    font-size: 76px; // 38px * 2 = 76px，适配750px基准
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 24px; // 12px * 2 = 24px
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
  }

  .login-subtitle {
    font-size: 36px; // 18px * 2 = 36px，适配750px基准
    color: #718096;
    font-weight: 400;
    line-height: 1.4;
  }
}

// 表单容器 - 750px 设计基准优化
.login-form-container {
  opacity: 0;
  transform: translateY(60px); // 增大动画位移
  transition: all 0.6s ease-out 0.2s;

  &.form-visible {
    opacity: 1;
    transform: translateY(0);
  }
}

// 表单样式 - 750px 设计基准优化 + Vant 组件样式覆盖
.login-form {
  .input-group {
    margin-bottom: 64px; // 32px * 2 = 64px，增大间距

    // Vant Field 组件样式覆盖
    :deep(.custom-field) {
      background: #f7fafc;
      border: 4px solid #e2e8f0; // 2px * 2 = 4px，增大边框
      border-radius: 32px; // 16px * 2 = 32px，增大圆角
      padding: 0;
      min-height: 112px; // 56px * 2 = 112px，适配750px基准
      transition: all 0.3s ease;

      &:focus-within {
        border-color: #667eea;
        background: #ffffff;
        box-shadow: 0 0 0 6px rgba(102, 126, 234, 0.1); // 3px * 2 = 6px
        transform: translateY(-4px); // 2px * 2 = 4px
      }

      &.van-field--error {
        border-color: #e53e3e;
        background: #fed7d7;
        animation: shake 0.5s ease-in-out;
      }

      .van-field__control {
        padding: 40px 48px; // 20px * 2, 24px * 2，增大内边距
        font-size: 36px; // 18px * 2 = 36px，适配750px基准
        color: #2d3748;
        font-weight: 500;
        line-height: 1.4;
        border: none;
        background: transparent;

        &::placeholder {
          color: #a0aec0;
          font-weight: 400;
        }
      }

      .van-field__left-icon {
        margin-right: 32px; // 16px * 2 = 32px
        margin-left: 48px; // 24px * 2 = 48px
        display: flex;
        align-items: center;
      }

      .van-field__right-icon {
        margin-right: 48px; // 24px * 2 = 48px
        display: flex;
        align-items: center;
      }

      .van-field__error-message {
        display: none; // 使用自定义错误样式
      }
    }

    // 输入框图标样式 - 750px 设计基准优化
    .input-icon {
      color: #a0aec0;
      transition: color 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      svg {
        width: 48px; // 24px * 2 = 48px，适配750px基准
        height: 48px;
        display: block;
      }
    }

    // 密码切换按钮样式 - 750px 设计基准优化
    .password-toggle {
      background: none;
      border: none;
      color: #a0aec0;
      cursor: pointer;
      padding: 16px; // 8px * 2 = 16px
      border-radius: 16px; // 8px * 2 = 16px
      transition: all 0.3s ease;
      min-width: 88px; // 44px * 2 = 88px，保持触摸目标标准
      min-height: 88px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        color: #667eea;
        background: rgba(102, 126, 234, 0.1);
      }

      svg {
        width: 48px; // 24px * 2 = 48px
        height: 48px;
        display: block;
      }
    }

    // 焦点状态下的图标颜色变化
    :deep(.custom-field:focus-within) {
      .input-icon {
        color: #667eea;
      }
    }

    .error-message {
      color: #e53e3e;
      font-size: 28px; // 14px * 2 = 28px，适配750px基准
      margin-top: 16px; // 8px * 2 = 16px
      margin-left: 8px; // 4px * 2 = 8px
      font-weight: 500;
    }
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-10px); // 5px * 2 = 10px，适配750px基准
  }
  75% {
    transform: translateX(10px); // 5px * 2 = 10px
  }
}

// 错误信息动画 - 750px 设计基准优化
.error-slide-enter-active,
.error-slide-leave-active {
  transition: all 0.3s ease;
}

.error-slide-enter-from {
  opacity: 0;
  transform: translateY(-20px); // 10px * 2 = 20px，适配750px基准
}

.error-slide-leave-to {
  opacity: 0;
  transform: translateY(-20px); // 10px * 2 = 20px
}

// 表单选项 - 750px 设计基准优化 + Vant 组件样式覆盖
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 80px; // 40px * 2 = 80px，增大间距
  padding: 0 8px; // 4px * 2 = 8px

  // Vant Checkbox 组件样式覆盖
  :deep(.custom-checkbox) {
    min-height: 88px; // 44px * 2 = 88px，保持触摸目标标准
    padding: 16px 0; // 8px * 2 = 16px
    font-size: 32px; // 16px * 2 = 32px，适配750px基准
    color: #4a5568;
    font-weight: 500;
    line-height: 1.4;

    .van-checkbox__icon {
      width: 48px; // 24px * 2 = 48px
      height: 48px;
      margin-right: 24px; // 12px * 2 = 24px

      .van-icon {
        width: 48px;
        height: 48px;
        border: 4px solid #e2e8f0; // 2px * 2 = 4px
        border-radius: 12px; // 6px * 2 = 12px
        background: #ffffff;
        transition: all 0.3s ease;

        &::before {
          color: #ffffff;
          font-size: 28px; // 14px * 2 = 28px
        }
      }
    }

    &.van-checkbox--checked .van-checkbox__icon .van-icon {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-color: #667eea;
    }

    &:hover .van-checkbox__icon .van-icon {
      border-color: #667eea;
      transform: scale(1.05);
    }
  }

  .forgot-password {
    background: none;
    border: none;
    color: #667eea;
    font-size: 32px; // 16px * 2 = 32px，适配750px基准
    font-weight: 600;
    cursor: pointer;
    padding: 24px 32px; // 12px * 2, 16px * 2
    border-radius: 16px; // 8px * 2 = 16px
    transition: all 0.3s ease;
    min-height: 88px; // 44px * 2 = 88px，保持触摸目标标准
    display: flex;
    align-items: center;

    &:hover {
      background: rgba(102, 126, 234, 0.1);
      transform: translateY(-2px); // 1px * 2 = 2px
    }
  }
}

// 登录按钮 - 750px 设计基准优化 + Vant 组件样式覆盖
:deep(.login-button) {
  width: 100%;
  padding: 40px 64px; // 20px * 2, 32px * 2
  min-height: 112px; // 56px * 2 = 112px，适配750px基准
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 32px; // 16px * 2 = 32px
  color: white;
  font-size: 36px; // 18px * 2 = 36px，适配750px基准
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  letter-spacing: 1px; // 0.5px * 2 = 1px
  display: flex;
  align-items: center;
  justify-content: center;

  // 覆盖 Vant Button 默认样式
  &.van-button {
    height: auto;
    line-height: normal;

    .van-button__content {
      font-size: 36px; // 18px * 2 = 36px
      font-weight: 600;
      letter-spacing: 1px;
    }

    .van-loading {
      color: white;
    }

    .van-loading__text {
      color: white;
      font-size: 36px; // 18px * 2 = 36px
      font-weight: 600;
      margin-left: 16px; // 8px * 2 = 16px
    }
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    transform: translateY(-4px); // 2px * 2 = 4px，适配750px基准
    box-shadow: 0 20px 50px rgba(102, 126, 234, 0.3); // 增强阴影效果

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
  }

  &.van-button--loading {
    cursor: not-allowed;
    opacity: 0.8;

    &:hover {
      transform: none;
      box-shadow: none;
    }
  }

  &:disabled,
  &.van-button--disabled {
    cursor: not-allowed;
    opacity: 0.6;

    &:hover {
      transform: none;
      box-shadow: none;
    }
  }
}

// 暗色模式支持
@media (prefers-color-scheme: dark) {
  .login-page {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  }

  .login-container {
    background: rgba(26, 32, 44, 0.95);
    color: #e2e8f0;
  }

  .login-header {
    .login-title {
      color: #e2e8f0;
    }

    .login-subtitle {
      color: #a0aec0;
    }
  }

  .login-form {
    // 暗色模式下的 Vant Field 组件样式
    :deep(.custom-field) {
      background: #2d3748;
      border-color: #4a5568;

      &:focus-within {
        background: #1a202c;
        border-color: #667eea;
      }

      &.van-field--error {
        background: #742a2a;
        border-color: #e53e3e;
      }

      .van-field__control {
        color: #e2e8f0;

        &::placeholder {
          color: #718096;
        }
      }
    }

    .input-icon {
      color: #718096;
    }

    .error-message {
      color: #fc8181;
    }
  }

  .form-options {
    // 暗色模式下的 Vant Checkbox 组件样式
    :deep(.custom-checkbox) {
      color: #e2e8f0;

      .van-checkbox__icon .van-icon {
        background: #2d3748;
        border-color: #4a5568;
      }
    }

    .forgot-password {
      color: #90cdf4;

      &:hover {
        background: rgba(144, 205, 244, 0.1);
      }
    }
  }
}
</style>
