<script setup>
import { getUserAPI, getUserInfoAPI, uploadFileAPI } from "@/api/user";

const { send: getUserInfoSend } = getUserInfoAPI();

const { send: getUserInfoSend2 } = getUserInfoAPI();

const value = ref("");

function getUserInfo() {
  getUserInfoSend({ id: 3 });
}

function getUserInfoSend2Btn() {
  getUserInfoSend2({ a: 1 });
}

const { upload, appendFiles } = uploadFileAPI();
async function uploadFileFn() {
  await appendFiles({
    multiple: true,
  });
  const response = await upload();
}

const { data } = getUserAPI(value, [value]);
</script>

<template>
  <div>
    <van-cell-group inset>
      <van-field v-model="value" label="文本" placeholder="请输入用户名" />
    </van-cell-group>
    <van-button type="primary" @click="getUserInfo"> 获取用户信息 </van-button>
    <van-button type="primary" @click="getUserInfoSend2Btn"> 获取用户信息1 </van-button>
    <van-button type="primary" @click="uploadFileFn"> 上传 </van-button>
  </div>
</template>
