<template>
  <div class="flex flex-col gap-y-[30px]">
    <div v-for="item in options" :key="item.title">
      <p class="text-[30px] text-[#99]">{{ item.title }}</p>
      <slot :name="item.slot || 'default'" :data="data">
        <p class="text-[30px] text-[#222] mt-[20px]">{{ item.format ? item.format(data) : data[item.value] || "-" }}</p>
      </slot>
    </div>
  </div>
</template>

<script setup>
defineOptions({
  name: "CellItem",
});
const props = defineProps({
  options: {
    type: Array,
    default: () => [],
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});
</script>

<style lang="scss" scoped></style>
