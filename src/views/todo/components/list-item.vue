<template>
  <div class="bg-white p-3.5 rounded-lg" @click="onClick">
    <p class="text-[30px] font-normal text-[#222] font-pingfang">
      <slot name="title">标题</slot>
    </p>
    <div class="bg-[#F6F6FD] rounded-sm p-3.5 mt-2.5 flex flex-col gap-2.5">
      <p class="flex" v-for="item in options" :key="item.value">
        <span class="text-[#999999] text-[24px] font-normal font-pingfang">{{ item.label }}：</span>
        <slot :name="item.slot" :data="data">
          <span class="text-[#222] text-[24px] font-normal flex-1">{{ item.format ? item.format(data) : data[item.value] || "-" }}</span>
        </slot>
      </p>
    </div>
    <div class="flex-bc mt-2.5">
      <p class="flex items-center" v-if="data.clzt === 0">
        <IconifyIconOnline icon="ri:close-circle-fill" class="w-[36px] h-[36px] text-[#F66A6A]" />
        <span class="text-[#F66A6A] text-[24px] pl-1 font-pingfang leading-[1.2]">未处理</span>
      </p>
      <p class="flex items-center" v-else>
        <IconifyIconOnline icon="ri:checkbox-circle-fill" class="w-[36px] h-[36px] text-[#26C462]" />
        <span class="text-[#26C462] text-[24px] pl-1 font-pingfang leading-[1.2]">已处理</span>
        <span class="text-[#9EA1AA] text-[24px] pl-2.5 leading-[1.2]" v-if="data.clsj">{{ data.clsj }}</span>
      </p>
      <van-button type="primary" size="mini" v-if="isBtn" @click.stop="onBtnClick">{{ btnName }}</van-button>
    </div>
  </div>
</template>

<script setup>
defineOptions({
  name: "ListItem",
});
const props = defineProps({
  btnName: {
    type: String,
    default: "查看",
  },
  options: {
    type: Array,
    default: () => [
      {
        label: "预警时间",
        value: "value",
      },
      {
        label: "预警时间",
        value: "value",
      },
    ],
  },
  data: {
    type: Object,
    default: () => ({
      value: "",
    }),
  },
  isBtn: {
    type: Boolean,
    default: true,
  },
});
const emit = defineEmits(["click", "btnClick"]);
const onClick = () => {
  emit("click", props.data);
};
const onBtnClick = () => {
  emit("btnClick", props.data);
};
</script>

<style lang="scss" scoped></style>
