<template>
  <div class="px-[24px] py-[20px] flex items-center gap-x-[8px]" v-if="options.length > 0">
    <template v-for="item in options" :key="item.value">
      <div v-if="item.type === 'select'" class="flex-1 flex items-center bg-[#F8F8F8] rounded-sm pl-2 pr-1.5 py-0.75 gap-x-1" @click="onOpenSelect(item)">
        <van-field class="!p-0 !text-[24px] !bg-[#F8F8F8]" readonly>
          <template #input>
            <input
              type="text"
              :id="`cm-input-${item.value}`"
              :value="getNameByValue(item)"
              readonly
              class="van-field__control text-[24px] w-full font-normal font-pingfang h-[40px]"
              :placeholder="item.placeholder || '全部'" />
          </template>
        </van-field>
        <IconifyIconOnline icon="ri:arrow-down-s-line" class="w-[48px] h-[52px] text-[#AFB3BF]" />
      </div>
      <div v-else-if="item.type === 'input'" class="flex-1 flex items-center bg-[#F8F8F8] rounded-sm pl-2 pr-1.5 py-1 gap-x-1">
        <van-search
          v-model="searchInfo[item.value]"
          :placeholder="item.placeholder || '搜索姓名'"
          left-icon=""
          @search="onSearch(item.value)"
          class="cm-search-input w-full !p-0 !text-[24px] !bg-[#F8F8F8]" />
        <IconifyIconOnline icon="ri:search-line" class="w-[48px] h-[48px] text-[#AFB3BF]" />
      </div>
    </template>

    <van-popup v-model:show="show" round position="bottom" teleport="body">
      <van-picker :title="`选择${currentItem.label}`" v-model="fieldValue" :columns="columns" @confirm="onConfirm" @cancel="onCancel" />
    </van-popup>
  </div>
</template>

<script setup>
import { getYxbListAPI, getNjListAPI, getZyszsjListAPI, getBjListAPI, getYjpcListAPI } from "@/api/common";
defineOptions({
  name: "SearchItem",
});

const searchInfo = reactive({});

const { data: yxbList, send: sendYxbList } = getYxbListAPI();
const { data: njList, send: sendNjList } = getNjListAPI();
const { data: zyszsjList, send: sendZyszsjList } = getZyszsjListAPI();
const { data: bjList, send: sendBjList } = getBjListAPI();
const { data: yjpcList, send: sendYjpcList } = getYjpcListAPI();
const responseData = ref({
  yx: yxbList,
  nj: njList,
  zy: zyszsjList,
  bj: bjList,
  yjpc: yjpcList,
});
const sendMethodByKey = {
  yx: sendYxbList,
  nj: sendNjList,
  zy: sendZyszsjList,
  bj: sendBjList,
  yjpc: sendYjpcList,
};
const show = ref(false);
const searchParams = defineModel("searchParams", {
  type: Object,
  default: () => ({}),
});
const props = defineProps({
  options: {
    type: Array,
    default: () => [],
  },
});

const onSearch = (key) => {
  Reflect.set(searchParams.value, key, searchInfo[key]);
};

// 根据配置来请求数据
props.options.filter(({ key, type }) => key && type === "select").forEach(({ key }) => sendMethodByKey[key]?.());

const getNameByValue = computed(() => (item) => {
  const data = Reflect.get(responseData.value, item.key);
  if (Array.isArray(data)) {
    const name = data.find((column) => column.value === Reflect.get(searchParams.value, item.value))?.text;
    return name;
  }
  return null;
});
const columns = ref([]);
const fieldValue = ref([]);
const currentItem = ref({});
const onOpenSelect = (item) => {
  currentItem.value = item;
  columns.value = Reflect.get(responseData.value, item.key);
  const searchValue = Reflect.get(searchParams.value, item.value);
  fieldValue.value = searchValue ? [searchValue] : [];
  show.value = true;
};
const onConfirm = ({ selectedValues }) => {
  Reflect.set(searchParams.value, currentItem.value.value, selectedValues.join(","));
  show.value = false;
};
const onCancel = () => (show.value = false);
</script>

<style lang="scss" scoped>
.cm-search-input {
  ::v-deep(.van-search__content) {
    padding: 0;
  }
  ::v-deep(.van-search__field) {
    height: unset;
  }
  ::v-deep(.van-field__control) {
    height: 40px;
  }
  ::v-deep(.van-field__body) {
    min-height: var(--van-cell-line-height);
  }
}
</style>
