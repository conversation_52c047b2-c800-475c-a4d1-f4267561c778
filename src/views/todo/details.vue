<template>
  <div class="todo-details bg-[#F0F2F9] w-full h-full box-border flex flex-col">
    <div class="overflow-y-auto flex-1 min-h-0 my-3.75">
      <van-collapse v-model="activeNames" class="custom-collapse">
        <van-collapse-item name="1">
          <template #title>
            <p class="custom-collapse-title text-[32px] text-[#333] font-pingfang relative">预警时间人员信息</p>
          </template>
          <div class="p-3.75 flex flex-col gap-y-3.75">
            <CellItem :options="yjsjOptions" :data="data" />
          </div>
        </van-collapse-item>

        <van-collapse-item name="2" class="mt-5">
          <template #title>
            <p class="custom-collapse-title text-[32px] text-[#333] font-pingfang relative">预警课程信息</p>
          </template>
          <div class="p-3.75 flex flex-col gap-y-3.75">
            <CellItem :options="yjkcOptions" :data="data">
              <template #kcbh="{ data }">
                <p class="text-[30px] text-[#222] mt-[20px]">{{ data.kcxnh }}{{ data.kcxqh === "1" ? "第一学期" : "第二学期" }}</p>
              </template>
            </CellItem>
          </div>
        </van-collapse-item>
      </van-collapse>
      <div class="mt-5 bg-white">
        <p class="custom-collapse-title relative h-[104px] flex items-center border-b border-[#E5E5E5]">
          <span class="text-[32px] text-[#333] font-pingfang">预警处理</span>
        </p>
        <div class="p-3.75">
          <p class="required relative text-[#999] text-[30px] font-normal font-pingfang">干预措施</p>
          <van-field
            v-model="data.yjxxCjClVO.clyj"
            class="tetx-[30px] text-[#222] !py-2.5 !px-[18px] font-pingfang"
            rows="2"
            :readonly="route.query.type !== 'handle'"
            autosize
            type="textarea"
            maxlength="500"
            show-word-limit
            placeholder="请输入" />
        </div>
      </div>
      <div class="mt-5 bg-white p-3.75">
        <p class="text-[#999] text-[30px] font-normal font-pingfang">再次提醒日期</p>
        <p class="pt-2.5 flex items-center justify-between" @click="onDateAgain">
          <span class="text-[#222] text-[30px] font-normal font-pingfang" :class="{ 'text-[#999]': !data.yjxxCjClVO.zctxsj }">{{ data.yjxxCjClVO.zctxsj || "请选择" }}</span>
          <van-icon name="arrow" size="20" color="#999" v-if="route.query.type === 'handle'" />
        </p>
      </div>

      <div class="mt-5 bg-white p-3.75 mb-3.75" v-if="data.clsj">
        <p class="text-[#999] text-[30px] font-normal font-pingfang">处理时间</p>
        <p class="mt-2.5 text-[#222] text-[30px] font-normal font-pingfang">{{ data.clsj }}</p>
      </div>
    </div>
    <div class="bg-white p-3.75" v-if="$route.query.type === 'handle'">
      <van-button block type="primary" size="small" @click="onSave" :loading="saveLoading" loading-text="保存中...">保 存</van-button>
    </div>
    <van-popup v-model:show="dateAgainShow" position="bottom" teleport="body">
      <van-date-picker title="选择日期" :min-date="minDate" :max-date="maxDate" @confirm="onConfirm" @cancel="dateAgainShow = false">
        <template #confirm>
          <span class="text-[#0E5FFF] font-pingfang active:text-[#1d64f2]">确定</span>
        </template>
      </van-date-picker>
    </van-popup>
  </div>
</template>

<script setup>
import CellItem from "./components/cell-item.vue";
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { showToast } from "vant";
import { yjxxDetailAPI, handleBatchAPI } from "@/api/todo";
defineOptions({
  name: "TodoDetails",
  title: "个人处理",
});
definePage({
  name: "TodoDetails",
  meta: {
    layout: "index",
    title: "详情",
    navBar: true,
    isAuth: true,
  },
});

const route = useRoute();
const { data } = yjxxDetailAPI(route.query);

const activeNames = ref([]);

const yjsjOptions = ref([
  {
    title: "预警时间",
    value: "yjsj",
  },
  {
    title: "年级",
    value: "nj",
  },
  {
    title: "学院",
    value: "xymc",
  },
  {
    title: "专业",
    value: "zymc",
  },
  {
    title: "班级",
    value: "bjmc",
  },
  {
    title: "学号",
    value: "xsxh",
  },
  {
    title: "姓名",
    value: "xsxm",
  },
]);
const yjkcOptions = ref([
  {
    title: "开课学年/学期",
    value: "",
    slot: "kcbh",
  },
  {
    title: "课程性质",
    value: "kcxzmc",
  },
  {
    title: "课程属性",
    value: "kcsxmc",
  },
  {
    title: "课程号",
    value: "kcbh",
  },
  {
    title: "课程",
    value: "kcmc",
  },
  {
    title: "实得学分",
    value: "sdxf",
    format: (row) => `${row.sdxf}（总100分）`,
  },
  {
    title: "实得成绩",
    value: "sdcj",
    format: (row) => `${row.sdcj}（总100分）`,
  },
  {
    title: "成绩类型",
    value: "cjlxmc",
  },
  {
    title: "是否必过",
    value: "sfbg",
    format: (row) => {
      if (row.sfbg === 1) {
        return "是";
      } else if (row.sfbg === 0) {
        return "否";
      } else {
        return "-";
      }
    },
  },
]);

// 再次提醒日期
const onDateAgain = () => {
  // 如果是处理，则可以再次提醒
  if (route.query.type === "handle") {
    dateAgainShow.value = true;
  }
};

const minDate = ref(new Date(new Date().setDate(new Date().getDate() + 1)));
const maxDate = ref(new Date(2099, 11, 31));
const dateAgainShow = ref(false);
const onConfirm = (date) => {
  data.value.yjxxCjClVO.zctxsj = date.selectedValues.join("-");
  dateAgainShow.value = false;
};

const saveLoading = ref(false);
const router = useRouter();
const onSave = () => {
  saveLoading.value = true;
  if (!data.value.yjxxCjClVO?.clyj) {
    showToast("请输入干预措施");
  } else {
    saveLoading.value = true;
    handleBatchAPI({
      ...route.query,
      clyj: data.value.yjxxCjClVO?.clyj,
      zctxsj: data.value.yjxxCjClVO?.zctxsj,
      xh: data.xsxh,
    })
      .then(() => {
        showToast("保存成功");
        router.back();
      })
      .finally(() => {
        saveLoading.value = false;
      });
  }
};
</script>

<style lang="scss" scoped>
.todo-details {
  .custom-collapse {
    ::v-deep(.van-cell) {
      padding: 0 30px 0 0;
      height: 104px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &::after {
        content: none;
      }
    }

    ::v-deep(.van-collapse-item__wrapper) {
      margin-top: 2px;
    }

    ::v-deep(.van-collapse-item__content) {
      padding: 0;
    }
  }

  .custom-collapse-title {
    padding-left: 28px;

    &::after {
      content: "";
      position: absolute;
      width: 10px;
      height: 38px;
      background: linear-gradient(180deg, #4ca6ff 0%, #256dff 100%);
      box-shadow: 0px 4 8px 0px #256dff;
      border-radius: 0px 8px 8px 0px;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .required {
    padding-left: 18px;

    &::after {
      content: "*";
      position: absolute;
      width: 18px;
      left: 0px;
      top: 50%;
      transform: translateY(-50%);
      height: 40px;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 32px;
      color: #ff3232;
      line-height: 40px;
      text-align: center;
    }
  }
}
</style>
