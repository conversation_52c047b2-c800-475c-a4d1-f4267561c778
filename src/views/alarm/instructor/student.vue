<template>
  <div class="bg-[#f0f2f9] h-full w-full flex flex-col">
    <div class="bg-white">
      <p class="bg-[#FFF8F0] p-2.5 text-[#EC8800] text-[24px] font-normal font-pingfang">【温馨提示】学生毕业的最终解释权归四川邮电职业技术学院所有</p>
      <SearchItem v-model:searchParams="searchParams" :options="searchOptions" />
      <TabsItem :options="tabsOptions" :businessActive="businessActive" @change="onTabBusinessClick" :countData="warnTypeCount" />
    </div>
    <div class="flex-1 min-h-0 px-[26px] overflow-y-auto my-3.75">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
        <div class="flex flex-col gap-y-[30px]">
          <ListItem v-for="item in list" :key="item" :options="listOptionsKey" :data="item" :btnName="item.clzt === 0 ? '处理' : '查看处理'" @btnClick="onBtnClick(item)">
            <template #title>
              <span>预警学生：{{ item.xsxm }}</span>
            </template>
          </ListItem>
        </div>
      </van-list>
    </div>
  </div>
</template>

<script setup>
import ListItem from "@/views/todo/components/list-item.vue";
import SearchItem from "@/views/todo/components/search-item.vue";
import TabsItem from "@/views/todo/components/tabs-item.vue";
import { getAlarmInstructorStudentListAPI, getAlarmWarnTypeCountAPI } from "@/api/alarm";
defineOptions({
  name: "InstructorStudent",
  title: "预警信息-辅导员-学生",
});
definePage({
  name: "InstructorStudent",
  meta: {
    layout: "index",
    title: "预警信息",
    navBar: true,
    isAuth: true,
  },
});

// 业务类型选择
const businessActive = ref("1");
const listOptionsKey = computed(() => {
  return listOptions.value[tabsOptions.value.find((item) => item.key === businessActive.value)?.listOptionsKey];
});
// 业务类型选择选项
const tabsOptions = ref([
  {
    label: "必修",
    key: "1",
    tabsCountKey: "cjyjsl",
    listOptionsKey: "bx",
  },
  {
    label: "选修",
    key: "4",
    tabsCountKey: "xxyjsl",
    listOptionsKey: "xx",
  },
  {
    label: "实习",
    key: "2",
    tabsCountKey: "dgsxyjsl",
    listOptionsKey: "bx",
  },
  {
    label: "毕设",
    key: "3",
    tabsCountKey: "bysjyjsl",
    listOptionsKey: "bx",
  },
]);

// 搜索参数
const searchParams = ref({
  xsxm: "",
});
// 搜索参数选项
const searchOptions = ref([
  {
    key: "xsxm",
    label: "搜索姓名",
    value: "xsxm",
    type: "input",
    placeholder: "搜索姓名",
  },
]);

// 列表选项
const listOptions = ref({
  bx: [
    {
      label: "预警批次",
      value: "yjbh",
    },
    {
      label: "预警课程",
      value: "kcmc",
    },
    {
      label: "开课学年/学期",
      value: "xnxq",
    },
    {
      label: "实得成绩",
      value: "sdcj",
    },
    {
      label: "成绩类型",
      value: "cjlxmc",
    },
  ],
  xx: [
    {
      label: "预警批次",
      value: "yjbh",
    },
    {
      label: "已修课程",
      value: "xxhgs",
      format: (row) => {
        return `${row.xxhgs || 0}门`;
      },
    },
    {
      label: "待修课程",
      value: "xxqks",
      format: (row) => {
        return `${row.xxqks || 0}门`;
      },
    },
  ],
});
// 列表
const list = ref([]);
// 加载状态
const loading = ref(false);
// 是否加载完成
const finished = ref(false);
const pageCurrent = ref(1);
const route = useRoute();
const onLoad = () => {
  // 异步更新数据
  // setTimeout 仅做示例，真实场景中一般为 ajax 请求
  getAlarmInstructorStudentListAPI({ ...route.query, ...searchParams.value, current: pageCurrent.value, size: 10, yjlx: businessActive.value }).then((res) => {
    if (pageCurrent.value === 1) {
      list.value = res.records;
    } else {
      list.value.push(...res.records);
    }
    // 加载状态结束
    loading.value = false;
    // 数据全部加载完成
    if (res.total <= list.value.length) {
      finished.value = true;
    } else {
      pageCurrent.value++;
    }
  });
};
// 预警类型统计
const warnTypeCount = ref({});
// 获取预警类型统计
const getWarnTypeCount = () => {
  getAlarmWarnTypeCountAPI({ yjbh: route.query.yjbh, bjbh: route.query.bjbh, xsxm: searchParams.value.xsxm }).then((res) => {
    warnTypeCount.value = res;
  });
};
// 获取预警类型统计
getWarnTypeCount();
// 刷新
const onRefresh = () => {
  // 清空列表数据
  finished.value = false;
  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  pageCurrent.value = 1;
  list.value.length = 0;
  onLoad();
};

// 业务类型选择
const onTabBusinessClick = (key) => {
  businessActive.value = key;
  onRefresh();
};

const router = useRouter();
const onBtnClick = (item) => {
  if (businessActive.value === "4") {
    router.push({
      path: "/todo/take-details",
      query: {
        id: item.id,
        yjbh: item.yjbh,
        xsxh: item.xsxh,
        sfxx: businessActive.value === "4" ? 1 : 0, // 是否选修 1是 0否
        kcbh: item.kcbh,
        type: item.clzt === 0 ? "handle" : "detail", // detail 详情，handle 处理
      },
    });
  } else {
    router.push({
      path: "/todo/details",
      query: {
        id: item.id,
        yjbh: item.yjbh,
        xsxh: item.xsxh,
        sfxx: businessActive.value === "4" ? 1 : 0, // 是否选修 1是 0否
        kcbh: item.kcbh,
        type: item.clzt === 0 ? "handle" : "detail", // detail 详情，handle 处理
      },
    });
  }
};
// 监听业务类型选择
watch(
  () => searchParams.value.xsxm,
  () => {
    getWarnTypeCount();
    onRefresh();
  },
);
</script>

<style lang="scss" scoped></style>
