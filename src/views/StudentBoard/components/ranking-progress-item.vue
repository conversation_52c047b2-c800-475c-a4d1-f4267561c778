<template>
  <div class="flex flex-col gap-3.5">
    <div
      class="flex items-center justify-between gap-2.5 "
      v-for="(item, index) in 5" :key="item">
      <p
        class=" w-[60px] h-[50px] flex items-center justify-center">
        <img v-if="index < 3"
          :src="$getAssetsFile(`ranking-icon-${index + 1}.png`)"
          alt="" class="w-[60px] h-[50px]">
        <span v-else
          class="text-base text-[#444] font-pingfang font-black">0{{
          index }}</span>
      </p>
      <div class="flex-1">
        <div class="flex items-center justify-between">
          <p class="flex items-center gap-2.5">
            <span
              class="text-sm text-[#444] font-pingfang">蒲娜琴</span>
          </p>
          <p class="flex items-center gap-2.5">
            <span
              class="text-[30px] text-[#FF4C00]">80%</span>
          </p>
        </div>
        <div
          class="h-[18px] bg-[#FBF9F7] rounded-[18px] mt-[10px] w-full">
          <p
            class="h-full bg-linear-to-r from-[#FFE4BB] to-[#FF984C] rounded-[18px] transition-all"
            :style="{ width: `${progress || 100}%` }">
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineOptions({
  name: 'RankingProgressItem',
})
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
})

const progress = ref(10)
</script>
