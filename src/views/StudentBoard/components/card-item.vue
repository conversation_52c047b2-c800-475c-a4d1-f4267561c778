<template>
  <div
    class="card-item rounded-lg bg-white border-1 border-white relative">
    <p
      class="card-item-hander rounded-t-lg w-full h-[92px] bg-linear-to-r flex items-center relative"
      :style="{ '--line-color': lineColor }"
      :class="[`from-[${fromColor}]`, `to-[${toColor}]`]">
      <span
        class="ml-[54px] font-pingfang text-base font-semibold text-[#222]">{{ title }}</span>
    </p>
    <slot />
  </div>
</template>

<script setup>
defineOptions({
  name: 'CardItem',
})
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  lineColor: {
    type: String,
    default: '#5D82F5',
  },
  fromColor: {
    type: String,
    default: '#F0F3FC',
  },
  toColor: {
    type: String,
    default: '#FFFFFF',
  },
})
</script>

<style lang="scss" scoped>
</style>