import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'
import SecureLS from 'secure-ls'

// 导入本地存储管理器，替代 SecureLS
// encryptionSecret:自定义密钥
const ls = new SecureLS({
  encryptionSecret: 'iVWKLK3jXe39X8w6sPDSRfsdGkpNDx6R',
})
const store = createPinia()
store.use(
  createPersistedState({
    storage: {
      setItem: (key, value) => {
        ls.set(key, value)
      },
      getItem: (key) => {
        return ls.get(key)
      },
    },
    key: id => `cm-pinia-${id}`,
  }),
)

export function setupStore(app) {
  app.use(store)
}
export { store }
