<script setup>
import { nextTick, onMounted, ref, unref } from 'vue'
import { useRoute } from 'vue-router'

defineOptions({
  name: 'LayoutFrame',
})

const loading = ref(true)
const currentRoute = useRoute()
const frameSrc = ref('')
const frameRef = ref(null)
if (unref(currentRoute.meta)?.frameSrc) {
  frameSrc.value = unref(currentRoute.meta)?.frameSrc
}
unref(currentRoute.meta)?.frameLoading === false && hideLoading()

function hideLoading() {
  loading.value = false
}

function init() {
  nextTick(() => {
    const iframe = unref(frameRef)
    if (!iframe)
      return
    const _frame = iframe
    if (_frame.attachEvent) {
      _frame.attachEvent('onload', () => {
        hideLoading()
      })
    }
    else {
      iframe.onload = () => {
        hideLoading()
      }
    }
  })
}

onMounted(() => {
  init()
})
</script>

<template>
  <div v-loading="loading" class="frame" element-loading-text="加载中...">
    <iframe ref="frameRef" :src="frameSrc" class="frame-iframe" />
  </div>
</template>

<style lang="scss" scoped>
.frame {
  position: absolute;
  inset: 0;

  .frame-iframe {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border: 0;
  }
}

.main-content {
  margin: 2px 0 0 !important;
}
</style>
