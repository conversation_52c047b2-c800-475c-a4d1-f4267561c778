<script setup>
import { useRouter } from 'vue-router'
import CmNavBar from '@/components/CmNavBar/index.vue'
import useAppStore from '@/store/modules/app.js'

const appStore = useAppStore()
const router = useRouter()

function onBack() {
  router.go(-1)
}
</script>

<template>
  <div class="w-full h-full overflow-hidden box-border flex flex-col">
    <CmNavBar v-if="$route.meta.navBar" :title="$route.meta.title || '标题'" left-arrow @click-left="onBack" />
    <div class="flex-1 overflow-auto relative min-w-0.5 min-h-0 bg-white dark:bg-gray-800">
      <RouterView v-slot="{ Component, route }">
        <KeepAlive :include="appStore.keepAlive">
          <component :is="Component" :key="route" />
        </KeepAlive>
      </RouterView>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.cm-container {
  .cm-main {
    .content-main {
      height: 100%;
      width: 100%;
      overflow-y: auto;
      box-sizing: border-box;
    }
  }
}
.scale-slide-enter-active,
.scale-slide-leave-active {
  position: absolute;
  transition: all 0.3s ease;
}

.scale-slide-enter-from {
  left: -100%;
  opacity: 0;
}

.scale-slide-enter-to {
  left: 0;
  opacity: 1;
}

.scale-slide-leave-from {
  transform: scale(1);
  opacity: 1;
}

.scale-slide-leave-to {
  transform: scale(0);
  opacity: 0;
}
</style>
