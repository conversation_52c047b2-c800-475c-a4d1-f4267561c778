/**
 * 判断是否为空
 */
export function validatenull(val) {
  if (typeof val == 'boolean') {
    return false
  }
  if (typeof val == 'number') {
    return false
  }
  if (Array.isArray(val)) {
    if (val.length === 0)
      return true
  }
  else if (val instanceof Object) {
    if (JSON.stringify(val) === '{}')
      return true
  }
  else {
    if (val === 'null' || val == null || val === 'undefined' || val === undefined || val === '')
      return true
    return false
  }
  return false
}

/**
 * 判断输入是否为有效的 JSON 或者 JSON 字符串
 * @param {any} val - 待判断的值
 * @returns {boolean} - 如果是对象或可解析的 JSON 字符串，返回 true；否则返回 false
 */
export function validatejson(val) {
  // 直接判断是否为对象（排除 null 和数组）
  if (val !== null && typeof val === 'object') {
    return true
  }

  // 尝试解析字符串为 JSON
  if (typeof val === 'string') {
    try {
      const obj = JSON.parse(val)
      // 解析后还需判断是否为对象或数组
      return obj !== null && typeof obj === 'object'
    }
    catch (error) {
      void error // 明确忽略错误信息
      return false
    }
  }

  // 非对象、非数组、非字符串，或者字符串不是 JSON
  return false
}
