import { crypto } from "@cm/utils/crypto";
import qs from "qs";

// qs库配置选项
const qsOptions = {
  encode: true, // 启用编码
  encodeValuesOnly: false, // 同时编码键和值
  arrayFormat: "repeat", // 数组格式：key=val1&key=val2
  skipNulls: false, // 不跳过null值
  addQueryPrefix: false, // 不自动添加?前缀
  allowDots: false, // 不允许点号表示法
  charset: "utf-8", // 字符编码
  charsetSentinel: false, // 不发送字符集标识
  delimiter: "&", // 参数分隔符
  strictNullHandling: true, // 严格处理null值
};

/**
 * 判断字符串是否是 base64 编码
 * @param {string} str - 需要判断的字符串
 * @returns {boolean} 返回是否是 base64 编码
 */
function isBase64(str) {
  // 空字符串不是 base64
  if (str === "" || str.trim() === "") {
    return false;
  }
  try {
    // 尝试对字符串进行 base64 解码再编码，比较结果是否相同
    return btoa(atob(str)) === str;
  } catch (error) {
    void error; // 明确忽略错误信息
    // 如果解码失败，说明不是合法的 base64 字符串
    return false;
  }
}

/**
 * 序列化对象并加密
 * @param {object} obj - 需要序列化的对象
 * @returns {string} 返回加密后的查询字符串
 */
export function stringifyQuery(obj) {
  // 如果对象为空，返回空字符串
  if (!obj || Object.keys(obj).length === 0) {
    return "";
  }

  // 使用qs库序列化对象
  const queryString = qs.stringify(obj, qsOptions);

  // 非加密模式直接返回查询字符串
  if (!globalThis._VITE_ROUTER_QUERY_ENCRYPT) {
    return queryString;
  }

  // 加密模式：加密序列化后的查询字符串
  return queryString ? `?${crypto.encrypt(queryString)}` : "";
}

/**
 * 解密并反序列化查询字符串
 * @param {string} query - 加密的查询字符串
 * @returns {object} 返回解析后的对象
 */
export function parseQuery(query) {
  // 非加密模式直接使用qs解析
  if (!globalThis._VITE_ROUTER_QUERY_ENCRYPT) {
    return qs.parse(query, qsOptions);
  }

  // 加密模式处理
  if (!query) {
    return {};
  }

  // 移除查询字符串前的特殊字符
  const cleanQuery = query.trim().replace(/^([?#&])/, "");
  if (!cleanQuery) {
    return {};
  }

  try {
    // 判断是否需要解密
    const decryptedQuery = isBase64(cleanQuery) ? crypto.decrypt(cleanQuery) : cleanQuery;

    // 使用qs库解析解密后的查询字符串
    return qs.parse(decryptedQuery, qsOptions);
  } catch (error) {
    console.error("查询字符串解析失败:", error);
    return {};
  }
}
