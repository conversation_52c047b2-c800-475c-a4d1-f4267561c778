import { accessTokenKey, refreshTokenKey } from "@/config/website";

/**
 * Token管理服务
 * 提供token的获取、缓存和清理功能
 */
class TokenService {
  /**
   * 获取用户token（带缓存机制）
   * @returns {Promise<string|null>} 用户token
   */
  getUserToken() {
    const accessToken = sessionStorage.getItem(accessTokenKey);
    const refreshToken = sessionStorage.getItem(refreshTokenKey);
    const token = accessToken || refreshToken;
    return token;
  }

  getAccessToken() {
    return sessionStorage.getItem(accessTokenKey);
  }

  getRefreshToken() {
    return sessionStorage.getItem(refreshTokenKey);
  }

  setAccessToken(token) {
    sessionStorage.setItem(accessTokenKey, token);
  }

  setRefreshToken(token) {
    sessionStorage.setItem(refreshTokenKey, token);
  }

  removeAccessToken() {
    sessionStorage.removeItem(accessTokenKey);
  }

  removeRefreshToken() {
    sessionStorage.removeItem(refreshTokenKey);
  }

  /**
   * 检查用户是否已登录
   * @returns {Promise<boolean>} 是否已登录
   */
  isAuthenticated() {
    const token = this.getUserToken();
    return Boolean(token);
  }
}

// 导出单例实例
export default new TokenService();
