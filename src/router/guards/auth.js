import { fistPage, loginExpired } from "@/config/website";
import tokenService from "../services/token";

/**
 * 认证相关路由守卫
 */

/**
 * 处理登录过期 - 返回登录页面路径
 * @returns {string} 登录页面路径
 */
export function handleLoginExpired() {
  return false;
}

/**
 * 检查认证状态的守卫
 * @param {object} to - 目标路由对象
 * @param {object} _from - 来源路由对象
 * @returns {Promise<boolean|string|object>} 守卫执行结果
 */
export function authGuard(to, _from) {
  // 如果路由有token参数，则设置token并跳转到首页
  if (to.query.token) {
    tokenService.setAccessToken(to.query.token);
    return true;
  }
  // 获取用户认证状态
  const isAuthenticated = tokenService.isAuthenticated();
  // 已登录用户访问登录页时重定向到首页
  if (isAuthenticated && to.path === loginExpired) {
    return { path: fistPage.path };
  }
  // 不需要认证的页面直接通过
  if (to.meta.isAuth === false) {
    return true;
  }
  // 未登录用户重定向到登录页
  if (!isAuthenticated) {
    return handleLoginExpired();
  }
  // 认证通过，继续执行后续守卫
  return true;
}
