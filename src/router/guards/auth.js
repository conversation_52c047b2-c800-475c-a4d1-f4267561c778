import { fistPage, loginExpired } from "@/config/website";
import tokenService from "../services/token";
import { showToast } from "vant";

/**
 * 认证相关路由守卫
 */

/**
 * 清理URL中的token参数
 * 使用原生JavaScript API移除URL中的敏感token信息，确保浏览器历史记录中不保留token
 */
function cleanTokenFromUrl() {
  // 检查是否支持History API
  if (window.history && window.history.replaceState) {
    // 获取当前URL对象
    const url = new URL(window.location.href);

    // 检查URL参数中是否包含token
    if (url.searchParams.has("token")) {
      // 删除token参数
      url.searchParams.delete("token");

      // 使用replaceState替换当前历史记录条目，不会在历史记录中留下包含token的URL
      // 第一个参数：状态对象（null表示不需要状态）
      // 第二个参数：标题（空字符串表示不改变标题）
      // 第三个参数：新的URL（不包含token参数）
      window.history.replaceState(null, "", url.toString());

      console.log("已清理URL中的token参数");
    }
  } else {
    // 如果不支持History API，在控制台输出警告
    console.warn("浏览器不支持History API，无法清理URL中的token参数");
  }
}

/**
 * 处理登录过期 - 返回登录页面路径
 * @returns {string} 登录页面路径
 */
export function handleLoginExpired() {
  showToast({
    message: "登录过期",
    duration: 5000,
  });
  return false;
}

/**
 * 检查认证状态的守卫
 * @param {object} to - 目标路由对象
 * @param {object} _from - 来源路由对象
 * @returns {Promise<boolean|string|object>} 守卫执行结果
 */
export function authGuard(to, _from) {
  // 如果路由有token参数，则设置token并跳转到首页
  if (to.query.token) {
    tokenService.setAccessToken(to.query.token);

    // 立即清理URL中的token参数，避免在浏览器历史记录中保留敏感信息
    cleanTokenFromUrl();

    return true;
  }
  // 获取用户认证状态
  const isAuthenticated = tokenService.isAuthenticated();
  // 已登录用户访问登录页时重定向到首页
  if (isAuthenticated && to.path === loginExpired) {
    return { path: fistPage.path };
  }
  // 不需要认证的页面直接通过
  if (to.meta.isAuth === false) {
    return true;
  }
  // 未登录用户重定向到登录页
  if (!isAuthenticated) {
    return handleLoginExpired();
  }
  // 认证通过，继续执行后续守卫
  return true;
}
