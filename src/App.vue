<script setup>
import useAppStore from "@/store/modules/app";

const appStore = useAppStore();
// 监听系统主题变化
// function listenSystemThemeChange() {
//   const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
//   // 监听系统主题变化
//   mediaQuery.addEventListener('change', (matchMedia) => {
//     appStore.setTheme(matchMedia.matches ? 'dark' : 'light')
//   })
// }
// listenSystemThemeChange()
</script>

<template>
  <div class="w-screen h-screen overflow-hidden font-poppins flex-c bg-white dark:bg-dark">
    <van-config-provider :theme="appStore.theme" class="w-screen h-screen overflow-hidden">
      <router-view v-slot="{ Component, route }">
        <component :is="Component" :key="route" />
      </router-view>
    </van-config-provider>
  </div>
</template>
