<template>
  <div ref="chartRef" class="chart-container"></div>
</template>

<script setup>
import * as echarts from 'echarts';
const props = defineProps({
  option: {
    type: Object,
    default: () => {},
  },
});

let chart = null;
const chartRef = useTemplateRef('chartRef');

const initChart = () => {
  chart = echarts.init(chartRef.value);
  chart.setOption(props.option);
};

onMounted(() => {
  initChart();
});

onUnmounted(() => {
  chart.dispose();
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>