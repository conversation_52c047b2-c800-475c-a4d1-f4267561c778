# Docker 部署指南

> 本指南专为 Docker 新手设计，将从基础概念开始，逐步指导你完成 Vue 项目的 Docker 部署。

## 📖 目录

- [Docker 基础概念](#-docker-基础概念)
- [项目 Docker 配置说明](#-项目-docker-配置说明)
- [快速开始](#-快速开始)
- [开发环境部署](#-开发环境部署)
- [生产环境部署](#-生产环境部署)
- [常用命令](#-常用命令)
- [常见问题与解决方案](#-常见问题与解决方案)
- [性能优化建议](#-性能优化建议)

## 🐳 Docker 基础概念

### 什么是 Docker？

Docker 是一个容器化平台，可以将应用程序及其依赖打包到一个轻量级、可移植的容器中。

### 核心概念

- **镜像 (Image)**：类似于虚拟机的模板，包含运行应用所需的所有文件
- **容器 (Container)**：镜像的运行实例，类似于运行中的虚拟机
- **Dockerfile**：用于构建镜像的脚本文件
- **Docker Compose**：用于定义和运行多容器应用的工具

### Docker 的优势

1. **环境一致性**：开发、测试、生产环境完全一致
2. **快速部署**：几秒钟内启动整个应用环境
3. **资源隔离**：每个容器独立运行，互不影响
4. **易于扩展**：可以轻松创建多个容器实例

## 🏗️ 项目 Docker 配置说明

### 文件结构

```
项目根目录/
├── Dockerfile          # 生产环境镜像配置
├── Dockerfile.dev      # 开发环境镜像配置
├── docker-compose.yml  # 多容器应用配置
├── .dockerignore       # Docker 构建忽略文件
├── nginx.conf          # Nginx 服务器配置
└── DOCKER-GUIDE.md     # 本指南文档
```

### 配置文件说明

#### Dockerfile（生产环境）
使用多阶段构建：
1. **构建阶段**：使用 Node.js 环境编译 Vue 应用
2. **运行阶段**：使用 Nginx 服务静态文件

#### Dockerfile.dev（开发环境）
专为开发设计：
- 支持热重载
- 包含开发工具
- 挂载源代码目录

#### docker-compose.yml
定义了多个服务：
- `dev`：开发环境服务
- `app`：生产环境服务
- 预留了数据库和 API 服务配置

## 🚀 快速开始

### 前置要求

1. **安装 Docker**：
   - Windows/macOS：下载 [Docker Desktop](https://www.docker.com/products/docker-desktop)
   - Linux：使用包管理器安装 Docker

2. **安装 Docker Compose**：
   - Docker Desktop 已包含
   - Linux 需要单独安装

3. **验证安装**：
   ```bash
   docker --version
   docker-compose --version
   ```

### 一键启动（推荐）

```bash
# 克隆项目到本地
git clone <your-repo-url>
cd ssw-app

# 启动生产环境
docker-compose up app

# 或启动开发环境
docker-compose up dev
```

## 🛠️ 开发环境部署

### 方法一：使用 Docker Compose（推荐）

```bash
# 启动开发环境
docker-compose up dev

# 后台运行
docker-compose up -d dev

# 查看日志
docker-compose logs -f dev
```

### 方法二：直接使用 Docker

```bash
# 构建开发镜像
docker build -f Dockerfile.dev -t vite-vue-dev .

# 运行开发容器
docker run -p 3000:3000 \
  -v $(pwd):/app \
  -v /app/node_modules \
  --name vue-dev \
  vite-vue-dev
```

### 开发环境特性

- ✅ **热重载**：代码修改立即生效
- ✅ **源码映射**：支持调试和错误追踪
- ✅ **开发工具**：ESLint、Prettier 等
- ✅ **快速启动**：依赖预安装

### 访问应用

开发服务器启动后，访问：[http://localhost:3000](http://localhost:3000)

## 🏭 生产环境部署

### 方法一：使用 Docker Compose（推荐）

```bash
# 构建并启动生产环境
docker-compose up --build app

# 后台运行
docker-compose up -d app
```

### 方法二：直接使用 Docker

```bash
# 构建生产镜像
docker build -t ssw-app .

# 运行生产容器
docker run -d \
  -p 9977:9977 \
  --name vue-app \
  ssw-app
```

### 生产环境特性

- ✅ **多阶段构建**：镜像体积小（约 20MB）
- ✅ **Nginx 服务**：高性能静态文件服务（端口9977）
- ✅ **Gzip 压缩**：减少传输数据量
- ✅ **缓存策略**：优化加载性能
- ✅ **安全加固**：非特权用户运行
- ✅ **健康检查**：自动监控服务状态

### 访问应用

生产服务器启动后，访问：[http://localhost:9977](http://localhost:9977)

## 📋 常用命令

### Docker 基础命令

```bash
# 查看所有镜像
docker images

# 查看运行中的容器
docker ps

# 查看所有容器（包括已停止的）
docker ps -a

# 停止容器
docker stop <container-name>

# 删除容器
docker rm <container-name>

# 删除镜像
docker rmi <image-name>

# 进入容器内部
docker exec -it <container-name> /bin/sh
```

### Docker Compose 命令

```bash
# 启动所有服务
docker-compose up

# 后台启动
docker-compose up -d

# 启动特定服务
docker-compose up <service-name>

# 停止所有服务
docker-compose down

# 重新构建服务
docker-compose build

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs <service-name>

# 清理未使用的资源
docker-compose down --volumes --rmi all
```

### 项目特定命令

```bash
# 开发环境
docker-compose up dev              # 启动开发服务器
docker-compose logs -f dev         # 查看开发日志

# 生产环境
docker-compose up --build app      # 构建并启动生产服务器
docker-compose exec app /bin/sh    # 进入生产容器

# 数据管理
docker-compose down --volumes       # 清理数据卷
docker-compose pull                 # 更新基础镜像
```

## 🔧 常见问题与解决方案

### 1. 端口冲突

**问题**：`bind: address already in use`

**解决方案**：
```bash
# 查看端口占用
lsof -i :3000  # macOS/Linux
netstat -ano | findstr :3000  # Windows

# 修改端口映射
docker run -p 3001:3000 vite-vue-dev
```

### 2. 权限问题（Linux）

**问题**：`permission denied while trying to connect to the Docker daemon socket`

**解决方案**：
```bash
# 将用户添加到 docker 组
sudo usermod -aG docker $USER

# 重新登录或执行
newgrp docker
```

### 3. 构建速度慢

**问题**：依赖安装缓慢

**解决方案**：
```bash
# 使用国内镜像源
# 在 Dockerfile 中添加：
# RUN npm config set registry https://registry.npmmirror.com/
```

### 4. 热重载不工作

**问题**：代码修改后页面不更新

**解决方案**：
```bash
# 确保正确挂载源码目录
docker run -v $(pwd):/app -v /app/node_modules vite-vue-dev
```

### 5. 容器内访问宿主机服务

**问题**：容器无法连接宿主机上的数据库等服务

**解决方案**：
```bash
# 使用特殊主机名
# Windows/macOS: host.docker.internal
# Linux: ********** 或 host.docker.internal (Docker 20.10+)
```

### 6. 内存不足

**问题**：构建过程中出现内存错误

**解决方案**：
```bash
# 增加 Docker Desktop 内存限制
# 或在构建时设置：
docker build --memory=4g -t ssw-app .
```

## ⚡ 性能优化建议

### 1. 镜像优化

```dockerfile
# 使用更小的基础镜像
FROM node:20-alpine AS builder

# 利用构建缓存
COPY package*.json ./
RUN npm install
COPY . .
```

### 2. 多阶段构建

```dockerfile
# 构建阶段
FROM node:20-alpine AS builder
# ... 构建代码

# 运行阶段
FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
```

### 3. .dockerignore 优化

```gitignore
# 排除不必要的文件
node_modules
.git
*.log
coverage
```

### 4. 层缓存优化

```dockerfile
# 先复制依赖文件
COPY package.json pnpm-lock.yaml ./
RUN pnpm install

# 再复制源码
COPY . .
RUN pnpm build
```

### 5. 生产环境配置

```bash
# 启用生产模式
NODE_ENV=production

# 使用 PM2 管理进程
# 启用 Gzip 压缩
# 配置 CDN
```

## 🔐 安全建议

### 1. 使用非特权用户

```dockerfile
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
USER nextjs
```

### 2. 最小化攻击面

```dockerfile
# 只安装必要的包
RUN apk add --no-cache curl

# 删除不需要的文件
RUN rm -rf /tmp/* /var/cache/apk/*
```

### 3. 定期更新镜像

```bash
# 定期拉取最新的基础镜像
docker pull node:20-alpine
docker pull nginx:alpine
```

## 📚 进阶主题

### 1. 多环境配置

创建不同环境的 docker-compose 文件：
- `docker-compose.yml`：开发环境
- `docker-compose.prod.yml`：生产环境
- `docker-compose.test.yml`：测试环境

### 2. CI/CD 集成

```yaml
# .github/workflows/docker.yml
name: Docker Build and Deploy
on:
  push:
    branches: [main]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Build Docker image
        run: docker build -t myapp .
```

### 3. 集群部署

使用 Docker Swarm 或 Kubernetes 进行集群部署。

### 4. 监控和日志

```yaml
# docker-compose.yml
services:
  app:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

## 🆘 获取帮助

- **Docker 官方文档**：https://docs.docker.com/
- **Vue.js 官方文档**：https://vuejs.org/
- **Nginx 配置指南**：https://nginx.org/en/docs/
- **Docker Compose 文档**：https://docs.docker.com/compose/

## 📝 结语

通过本指南，你应该能够成功地将 Vue 项目部署到 Docker 容器中。Docker 虽然概念较多，但掌握了基本用法后，会大大简化项目的部署和管理。

建议从开发环境开始，逐步熟悉 Docker 的使用，然后再尝试生产环境部署。记住，实践是最好的学习方式！

如果遇到问题，不要犹豫查阅文档或寻求帮助。Docker 社区非常活跃，大部分问题都能找到解决方案。 