---
description: 
globs: 
alwaysApply: false
---
# 路由架构

本项目使用Vue Router进行路由管理，采用了基于文件的路由系统结合动态路由生成。

## 路由配置

- [src/router/index.js](mdc:src/router/index.js) - 路由主配置文件
- [src/router/utils.js](mdc:src/router/utils.js) - 路由工具函数
- [src/router/modules/](mdc:src/router/modules) - 路由模块

## 路由功能

- 支持历史模式和哈希模式
- 自动路由生成 (`vue-router/auto-routes`)
- 路由布局系统 (`virtual:generated-layouts`)
- 路由守卫和权限控制
- 滚动行为管理
- 查询参数处理

## 布局系统

项目使用布局系统来管理页面的通用结构：

- [src/layout/index.vue](mdc:src/layout/index.vue) - 主布局组件

## 关键概念

- 路由守卫控制权限访问和未登录重定向
- 动态路由生成基于后端返回的菜单
- 使用事件系统处理登录过期

