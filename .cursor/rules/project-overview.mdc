---
description: 
globs: 
alwaysApply: false
---
# 项目概述

这是一个基于Vite和Vue 3的前端项目，使用了现代化的技术栈和工具链。

## 项目入口

- [index.html](mdc:index.html) - HTML入口文件
- [src/main.js](mdc:src/main.js) - JavaScript主入口文件
- [src/App.vue](mdc:src/App.vue) - Vue根组件

## 核心配置文件

- [vite.config.js](mdc:vite.config.js) - Vite配置
- [package.json](mdc:package.json) - 项目依赖和脚本

## 项目结构

- `src/` - 源代码目录
  - `assets/` - 静态资源（样式、图片等）
  - `components/` - Vue组件
  - `views/` - 页面视图
  - `router/` - 路由配置
  - `store/` - Pinia状态管理
  - `utils/` - 工具函数
  - `api/` - API请求
  - `layout/` - 布局组件
  - `directives/` - Vue自定义指令
  - `plugins/` - 插件
  - `config/` - 项目配置

## 技术栈

- Vue 3 - 前端框架
- Vite - 构建工具
- Vue Router - 路由管理
- Pinia - 状态管理
- Vant - UI组件库
- TailwindCSS - 原子CSS框架
- Axios - HTTP客户端

