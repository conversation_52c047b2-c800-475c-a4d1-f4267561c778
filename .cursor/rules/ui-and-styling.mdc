---
description: 
globs: 
alwaysApply: false
---
# UI和样式

本项目使用Vant UI库和TailwindCSS作为UI和样式解决方案，结合了组件库的易用性和原子CSS的灵活性。

## 样式文件

- [src/assets/styles/global.scss](mdc:src/assets/styles/global.scss) - 全局样式
- [src/assets/styles/tailwind.css](mdc:src/assets/styles/tailwind.css) - Tailwind样式
- [src/assets/styles/vant.scss](mdc:src/assets/styles/vant.scss) - Vant组件样式
- [src/assets/styles/vant-dark.scss](mdc:src/assets/styles/vant-dark.scss) - Vant暗黑模式样式
- [src/assets/styles/var.scss](mdc:src/assets/styles/var.scss) - SCSS变量

## UI组件

项目使用Vant作为主要UI组件库：

```js
// 自动导入Vant组件
Components({
  resolvers: [VantResolver()],
})

// 手动导入特定组件样式
import 'vant/es/toast/style'
import 'vant/es/dialog/style'
import 'vant/es/notify/style'
import 'vant/es/image-preview/style'
```

## 图标系统

项目使用`@iconify/vue`作为图标库：

```js
// 全局注册图标组件
import {
  FontIcon,
  IconifyIconOffline,
  IconifyIconOnline,
} from './components/ReIcon/index'

app.component('IconifyIconOffline', IconifyIconOffline)
app.component('IconifyIconOnline', IconifyIconOnline)
app.component('FontIcon', FontIcon)
```

## 主题系统

项目支持亮色和暗色主题切换：

```html
<van-config-provider :theme="appStore.theme" class="w-screen h-screen overflow-hidden">
  <router-view v-slot="{ Component, route }">
    <component :is="Component" :key="route" />
  </router-view>
</van-config-provider>
```

## 移动端适配

使用`postcss-mobile-forever`插件进行移动端适配：

```js
// postcss.config.js
module.exports = {
  plugins: {
    'postcss-mobile-forever': {
      viewportWidth: 375,
    }
  }
}
```

