{
  "Vue3.3+普通组件模板": {
    "scope": "vue",
    "prefix": "vue3+PascalCase带父目录",
    "body": [
      "<template>",
      "\t<div>${TM_DIRECTORY/^.*[\\\\\\/]([^\\\\\\/]+)$/${1:/pascalcase}/}${TM_FILENAME_BASE/(.*)/${1:/pascalcase}/}</div>",
      "</template>\n",
      "<script setup>",
      "defineOptions({",
      "\tname: '${TM_DIRECTORY/^.*[\\\\\\/]([^\\\\\\/]+)$/${1:/pascalcase}/}${TM_FILENAME_BASE/(.*)/${1:/pascalcase}/}'",
      "})",
      "definePage({",
      "\tname: '${TM_DIRECTORY/^.*[\\\\\\/]([^\\\\\\/]+)$/${1:/pascalcase}/}${TM_FILENAME_BASE/(.*)/${1:/pascalcase}/}',",
      "\tmeta: {",
      "\t\tlayout: 'index',",
      "\t\ttitle: '${1:页面标题}',",
      "\t\tnavBar: true,",
      "\t\tisAuth: false,",
      "\t}",
      "})",
      "</script>\n",
      "<style lang='scss' scoped>\n",
      "</style>",
    ],
    "description": "Vue3.3+普通组件模板 - 使用文件名作为组件名（适用于非index.vue文件）"
  },
}
