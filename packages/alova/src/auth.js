import { mitter } from "@cm/utils";
import { createServerTokenAuthentication } from "alova/client";
import { refreshTokenAPI } from "@/api/user.js";
import { accessTokenKey, refreshTokenKey, tokenHeader, tokenType } from "@/config/website.js";
import tokenService from "@/router/services/token.js";

/**
 * 创建Token认证拦截器
 */
export const { onAuthRequired, onResponseRefreshToken } = createServerTokenAuthentication({
  // Token刷新策略，响应时检测是否需要刷新token
  refreshTokenOnSuccess: {
    // 判断token是否过期（401状态码）
    isExpired: (response) => {
      return response.status === 401;
    },
    // 当token过期时刷新token
    handler: async () => {
      try {
        const refreshToken = tokenService.getRefreshToken();
        const res = await refreshTokenAPI(refreshToken);
        if (res?.success !== false) {
          tokenService.setAccessToken(Reflect.get(res, accessTokenKey));
          tokenService.setRefreshToken(Reflect.get(res, refreshTokenKey));
        } else {
          mitter.emit("ROUTER_LOGIN_EXPIRED", () => {});
          throw new Error(res.error_description || "刷新Token失败");
        }
      } catch (error) {
        console.error("Token刷新失败:", error);
        mitter.emit("ROUTER_LOGIN_EXPIRED", () => {});
        throw error;
      }
    },
  },
  refreshTokenOnError: {
    // 响应时触发，可获取到error和method，并返回boolean表示token是否过期
    // 当服务端返回401时，表示token过期
    isExpired: (error, method) => {
      void method;
      return error.status === 401;
    },

    // 当token过期时触发，在此函数中触发刷新token
    handler: async (error, method) => {
      void method;
      void error;
    },
  },
  // 登录成功时保存token
  login: async (response) => {
    if (response.status === 200) {
      const res = await response.clone().json();
      if (res.access_token) {
        tokenService.setAccessToken(Reflect.get(res, accessTokenKey));
        tokenService.setRefreshToken(Reflect.get(res, refreshTokenKey));
      }
    }
  },
  // 请求时附加token到请求头
  assignToken: async (method) => {
    try {
      const token = tokenService.getAccessToken();
      if (token) {
        method.config.headers[tokenHeader] = `${tokenType} ${token}`;
      }
    } catch (error) {
      console.error("获取Token失败:", error);
    }
  },

  // 登出处理
  logout() {
    mitter.emit("ROUTER_LOGIN_EXPIRED", () => {});
  },
});

/**
 * 获取当前token
 * @returns {Promise<string|null>} 当前token或null
 */
export function getCurrentToken() {
  return tokenService.getAccessToken();
}

/**
 * 清除所有认证信息
 * @returns {Promise<void>}
 */
export async function clearAuthInfo() {
  tokenService.removeAccessToken();
  tokenService.removeRefreshToken();
}
