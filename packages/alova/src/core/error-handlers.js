/**
 * 错误处理器核心模块
 *
 * 使用策略模式重构错误处理系统
 * 提供可扩展、可测试的错误处理架构
 *
 * <AUTHOR>
 * @version 2.0.0
 */

import httpLoadingManager from "../loading-manager.js";
import logger from "../utils/logger.js";

/**
 * 错误严重级别枚举
 */
export const ERROR_SEVERITY = {
  LOW: "low", // 低级别：不影响核心功能
  MEDIUM: "medium", // 中级别：影响部分功能
  HIGH: "high", // 高级别：影响核心功能
  CRITICAL: "critical", // 严重：系统无法正常运行
};

/**
 * 错误处理结果接口
 * @typedef {object} ErrorHandleResult
 * @property {string} message - 处理后的错误消息
 * @property {string} severity - 错误严重级别
 * @property {object} metadata - 错误元数据
 */

/**
 * 错误处理器抽象基类
 *
 * 定义错误处理器的标准接口和通用行为
 * 所有具体错误处理器都应继承此类
 *
 * @abstract
 */
export class BaseErrorHandler {
  /**
   * 构造函数
   * @param {string} errorType - 错误类型标识
   * @param {string} severity - 错误严重级别
   */
  constructor(errorType, severity = ERROR_SEVERITY.MEDIUM) {
    if (new.target === BaseErrorHandler) {
      throw new Error("BaseErrorHandler是抽象类，不能直接实例化");
    }

    this.errorType = errorType;
    this.severity = severity;
    this.handledCount = 0;
    this.lastHandledTime = null;
  }

  /**
   * 处理错误的抽象方法
   *
   * @abstract
   * @param {Error} error - 错误对象
   * @param {string} customMessage - 自定义错误消息
   * @param {object} context - 错误上下文
   * @returns {ErrorHandleResult} 错误处理结果
   */
  handle(_error, _customMessage, _context) {
    const error = new Error("子类必须实现handle方法");
    throw error;
  }

  /**
   * 检查是否能处理该错误
   *
   * @abstract
   * @param {Error} error - 错误对象
   * @returns {boolean} 是否能处理
   */
  canHandle(_error) {
    const error = new Error("子类必须实现canHandle方法");
    throw error;
  }

  /**
   * 通用错误处理逻辑
   *
   * @protected
   * @param {Error} error - 错误对象
   * @param {string} message - 处理后的消息
   * @param {boolean} showToast - 是否显示Toast
   * @param {object} options - 处理选项
   * @returns {ErrorHandleResult} 处理结果
   */
  _createHandleResult(_error, message, showToast = true, options = {}) {
    // 更新统计信息
    this.handledCount++;
    this.lastHandledTime = Date.now();

    // 默认处理选项
    const defaultOptions = {
      metadata: {},
      ...options,
    };

    // 显示用户提示
    if (showToast) {
      httpLoadingManager.addErrorToast(message);
    }

    // 记录错误日志
    logger.debug(`错误处理器 ${this.errorType} 处理错误:`, {
      message,
      severity: this.severity,
      handledCount: this.handledCount,
      ...defaultOptions,
    });

    return {
      message,
      severity: this.severity,
      metadata: {
        handlerType: this.errorType,
        handledAt: this.lastHandledTime,
        handledCount: this.handledCount,
        ...defaultOptions.metadata,
      },
    };
  }

  /**
   * 获取处理器统计信息
   * @returns {object} 统计信息
   */
  getStats() {
    return {
      errorType: this.errorType,
      severity: this.severity,
      handledCount: this.handledCount,
      lastHandledTime: this.lastHandledTime,
    };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.handledCount = 0;
    this.lastHandledTime = null;
  }
}

/**
 * 网络错误处理器
 */
export class NetworkErrorHandler extends BaseErrorHandler {
  constructor() {
    super("NETWORK", ERROR_SEVERITY.HIGH);
  }

  canHandle(error) {
    const { name, message = "" } = error || {};
    const networkIndicators = ["Network", "fetch", "NETWORK_ERROR", "Failed to fetch"];

    return name === "TypeError" && networkIndicators.some((indicator) => message.includes(indicator));
  }

  handle(error, customMessage, context) {
    const message = customMessage || "网络连接失败，请检查网络设置";

    return this._createHandleResult(error, message, true, {
      metadata: {
        networkError: true,
        url: context?.url,
        timestamp: Date.now(),
      },
    });
  }
}

/**
 * 超时错误处理器
 */
export class TimeoutErrorHandler extends BaseErrorHandler {
  constructor() {
    super("TIMEOUT", ERROR_SEVERITY.MEDIUM);
  }

  canHandle(error) {
    const { name, message = "" } = error || {};
    const timeoutIndicators = ["timeout", "超时", "AbortError", "TimeoutError"];

    return name === "AbortError" || timeoutIndicators.some((indicator) => message.toLowerCase().includes(indicator.toLowerCase()));
  }

  handle(error, customMessage, context) {
    const message = customMessage || "请求超时，请检查网络连接";

    return this._createHandleResult(error, message, true, {
      metadata: {
        timeoutError: true,
        url: context?.url,
        timeout: context?.timeout || "unknown",
      },
    });
  }
}

/**
 * HTTP状态错误处理器
 */
export class HttpStatusErrorHandler extends BaseErrorHandler {
  constructor() {
    super("HTTP_STATUS", ERROR_SEVERITY.HIGH);
  }

  canHandle(error) {
    return error && typeof error.status === "number" && error.status >= 400;
  }

  handle(error, customMessage, context) {
    const status = error.status || "unknown";
    const message = customMessage || error.message || `服务器错误 (${status})`;

    return this._createHandleResult(error, message, true, {
      metadata: {
        httpStatus: status,
        url: context?.url,
        statusCategory: this._categorizeStatus(status),
      },
    });
  }

  /**
   * 分类HTTP状态码
   * @private
   */
  _categorizeStatus(status) {
    if (status >= 400 && status < 500) return "client_error";
    if (status >= 500 && status < 600) return "server_error";
    return "unknown";
  }
}

/**
 * 业务错误处理器
 */
export class BusinessErrorHandler extends BaseErrorHandler {
  constructor() {
    super("BUSINESS", ERROR_SEVERITY.LOW);
  }

  canHandle(error) {
    // 业务错误通常有特定的错误码
    return error && (error.code || error.error_code) && !error.status;
  }

  handle(error, customMessage, context) {
    const message = customMessage || error?.message || "业务处理失败";

    return this._createHandleResult(error, message, true, {
      metadata: {
        businessError: true,
        errorCode: error.code || error.error_code,
        url: context?.url,
      },
    });
  }
}

/**
 * 未知错误处理器（兜底处理器）
 */
export class UnknownErrorHandler extends BaseErrorHandler {
  constructor() {
    super("UNKNOWN", ERROR_SEVERITY.MEDIUM);
  }

  canHandle(_error) {
    // 兜底处理器，始终可以处理
    return true;
  }

  handle(error, customMessage, context) {
    const message = customMessage || error?.message || "请求发生未知错误";

    return this._createHandleResult(error, message, true, {
      metadata: {
        unknownError: true,
        errorName: error?.name,
        url: context?.url,
      },
    });
  }
}
