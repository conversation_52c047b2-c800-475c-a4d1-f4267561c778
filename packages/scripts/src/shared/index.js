/**
 * Execute command using execa
 * @param {string} cmd - Command to execute
 * @param {string[]} [args=[]] - Command arguments
 * @param {object} [options={}] - Execution options
 * @returns {Promise<string>} Command output
 */
export async function execCommand(cmd, args = [], options = {}) {
  const { execa } = await import('execa')
  const res = await execa(cmd, args, options)
  return res?.stdout?.trim() ?? ''
}

/**
 * Execute command with error handling
 * @param {string} cmd - Command to execute
 * @param {string[]} [args=[]] - Command arguments
 * @param {object} [options={}] - Execution options
 * @returns {Promise<{success: boolean, output: string, error?: Error}>} Execution result
 */
export async function execCommandSafe(cmd, args = [], options = {}) {
  try {
    const output = await execCommand(cmd, args, options)
    return { success: true, output }
  } catch (error) {
    return { success: false, output: '', error }
  }
}

/**
 * Check if a command exists in the system
 * @param {string} cmd - Command to check
 * @returns {Promise<boolean>} Whether the command exists
 */
export async function commandExists(cmd) {
  const { success } = await execCommandSafe('which', [cmd])
  return success
}
