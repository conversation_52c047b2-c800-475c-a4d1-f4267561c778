import { execCommand } from '../shared/index.js'

/**
 * Update package.json dependencies versions using npm-check-updates
 * @param {string[]} [args=['--deep', '-u']] - Command arguments for ncu
 * @returns {Promise<void>} Promise that resolves when update is complete
 */
export async function updatePkg(args = ['--deep', '-u']) {
  console.log('📦 正在更新依赖版本...')
  try {
    await execCommand('npx', ['ncu', ...args], { stdio: 'inherit' })
    console.log('✅ 依赖更新完成！')
  } catch (error) {
    console.error('❌ 依赖更新失败:', error.message)
    throw error
  }
}
