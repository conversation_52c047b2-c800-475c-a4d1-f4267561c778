import { rimraf } from 'rimraf'

/**
 * 清理指定的文件和目录
 * @param {string[]} paths - 要清理的路径数组
 * @returns {Promise<void>} Promise that resolves when cleanup is complete
 * @throws {Error} If cleanup fails
 */
export async function cleanup(paths) {
  console.log('🧹 开始清理...')
  console.log('清理路径:', paths)

  try {
    await rimraf(paths, { glob: true })
    console.log('✅ 清理完成！')
  } catch (error) {
    console.error('❌ 清理失败:', error.message)
    throw error
  }
}
