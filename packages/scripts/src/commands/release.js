import process from 'node:process'
import { versionBump } from 'bumpp'
import { execCommand, execCommandSafe } from '../shared/index.js'

/**
 * 图标定义常量
 * @type {object}
 */
const ICONS = {
  SUCCESS: '✅',
  ERROR: '❌',
  WARNING: '⚠️',
  INFO: 'ℹ️',
  DEBUG: '🔧',
  ROCKET: '🚀',
  ROLLBACK: '🔄',
  SAVE: '💾',
  RESTORE: '📦',
}

/**
 * 发布状态枚举
 */
const RELEASE_STATES = {
  IDLE: 'idle',
  PREPARING: 'preparing',
  BUMPING: 'bumping',
  EXECUTING: 'executing',
  COMMITTING: 'committing',
  TAGGING: 'tagging',
  PUSHING: 'pushing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
}

/**
 * 简化的信号处理器类 - 专门用于发布流程
 */
class ReleaseSignalHandler {
  constructor(options = {}) {
    this.isActive = false
    this.logger = options.logger || console
    this.exitCallback = options.exitCallback || (() => {})
    this.boundHandlers = new Map()
  }

  /**
   * 处理信号
   * @param {string} signal - 信号名称
   */
  handleSignal(signal) {
    this.logger.info(`${ICONS.DEBUG} 收到信号: ${signal}`)

    // 立即停止监听，避免重复触发
    this.stop()

    try {
      // 使用 setImmediate 确保回调在下一个事件循环中执行
      setImmediate(() => {
        this.exitCallback(signal)
      })
    }
    catch (error) {
      this.logger.error(`${ICONS.ERROR} 退出回调执行失败:`, error.message)
    }
  }

  /**
   * 启动信号监听
   * @returns {boolean} 是否成功启动
   */
  start() {
    if (this.isActive) {
      this.logger.warn(`${ICONS.WARNING} 信号处理器已经在运行`)
      return true
    }

    try {
      // 监听常见的中断信号
      const signals = ['SIGINT', 'SIGTERM']

      for (const signal of signals) {
        const handler = () => this.handleSignal(signal)
        this.boundHandlers.set(signal, handler)
        process.on(signal, handler)
      }

      this.isActive = true
      this.logger.info(`${ICONS.INFO} 信号处理器已启动 (Ctrl+C 可取消发布)`)
      return true
    }
    catch (error) {
      this.logger.error(`${ICONS.ERROR} 启动信号处理器失败:`, error.message)
      return false
    }
  }

  /**
   * 停止信号监听
   */
  stop() {
    if (!this.isActive)
      return

    try {
      // 移除所有信号监听器
      for (const [signal, handler] of this.boundHandlers) {
        process.removeListener(signal, handler)
      }

      this.boundHandlers.clear()
      this.isActive = false

      this.logger.debug(`${ICONS.DEBUG} 信号处理器已停止`)
    }
    catch (error) {
      this.logger.error(`${ICONS.ERROR} 停止信号处理器失败:`, error.message)
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.stop()
  }

  /**
   * 检查是否支持信号处理
   * @returns {boolean} 是否支持信号处理
   */
  static isSupported() {
    return typeof process.on === 'function'
  }
}

/**
 * Git状态管理器 - 用于版本回退功能
 */
class GitStateManager {
  constructor() {
    this.logger = console
    this.initialState = null
    this.backupCreated = false
  }

  /**
   * 保存当前Git状态
   * @returns {Promise<boolean>} 是否成功保存状态
   */
  async saveCurrentState() {
    try {
      this.logger.info(`${ICONS.SAVE} 正在保存当前Git状态...`)

      // 获取当前分支
      const currentBranch = await execCommand('git', ['rev-parse', '--abbrev-ref', 'HEAD'])

      // 获取当前提交哈希
      const currentCommit = await execCommand('git', ['rev-parse', 'HEAD'])

      // 检查是否有未提交的更改
      const { success: hasChanges } = await execCommandSafe('git', ['diff', '--quiet'])
      const hasUncommittedChanges = !hasChanges

      // 检查是否有暂存的更改
      const { success: hasStaged } = await execCommandSafe('git', ['diff', '--cached', '--quiet'])
      const hasStagedChanges = !hasStaged

      // 获取最近的标签
      const { success: tagSuccess, output: latestTag } = await execCommandSafe('git', ['describe', '--tags', '--abbrev=0'])

      this.initialState = {
        branch: currentBranch,
        commit: currentCommit,
        hasUncommittedChanges,
        hasStagedChanges,
        latestTag: tagSuccess ? latestTag : null,
        timestamp: new Date().toISOString(),
      }

      this.logger.debug(`${ICONS.DEBUG} Git状态已保存:`, {
        branch: this.initialState.branch,
        commit: this.initialState.commit.substring(0, 8),
        latestTag: this.initialState.latestTag,
      })

      this.backupCreated = true
      return true
    }
    catch (error) {
      this.logger.error(`${ICONS.ERROR} 保存Git状态失败:`, error.message)
      return false
    }
  }

  /**
   * 回退到初始状态
   * @returns {Promise<boolean>} 是否成功回退
   */
  async rollbackToInitialState() {
    if (!this.backupCreated || !this.initialState) {
      this.logger.warn(`${ICONS.WARNING} 没有可用的备份状态，无法回退`)
      return false
    }

    try {
      this.logger.info(`${ICONS.ROLLBACK} 正在回退到初始状态...`)

      // 重置到初始提交
      await execCommand('git', ['reset', '--hard', this.initialState.commit])
      this.logger.info(`${ICONS.RESTORE} 已重置到提交: ${this.initialState.commit.substring(0, 8)}`)

      // 删除可能创建的标签
      if (this.initialState.latestTag) {
        const { success: currentTagSuccess, output: currentTag } = await execCommandSafe('git', ['describe', '--tags', '--abbrev=0'])

        if (currentTagSuccess && currentTag !== this.initialState.latestTag) {
          this.logger.info(`${ICONS.ROLLBACK} 正在删除新创建的标签: ${currentTag}`)
          await execCommandSafe('git', ['tag', '-d', currentTag])

          // 如果标签已推送到远程，尝试删除远程标签
          const { success: remoteExists } = await execCommandSafe('git', ['ls-remote', '--tags', 'origin', currentTag])
          if (remoteExists) {
            this.logger.info(`${ICONS.ROLLBACK} 正在删除远程标签: ${currentTag}`)
            await execCommandSafe('git', ['push', 'origin', `:refs/tags/${currentTag}`])
          }
        }
      }

      // 如果有新的提交，尝试重置远程分支
      const { success: remoteExists } = await execCommandSafe('git', ['ls-remote', '--heads', 'origin', this.initialState.branch])
      if (remoteExists) {
        this.logger.info(`${ICONS.ROLLBACK} 正在重置远程分支状态...`)
        await execCommandSafe('git', ['push', 'origin', `+${this.initialState.commit}:${this.initialState.branch}`])
      }

      this.logger.info(`${ICONS.SUCCESS} Git状态回退完成`)
      return true
    }
    catch (error) {
      this.logger.error(`${ICONS.ERROR} Git状态回退失败:`, error.message)
      this.logger.error(`${ICONS.WARNING} 请手动检查并修复Git状态`)
      return false
    }
  }

  /**
   * 清理备份状态
   */
  cleanup() {
    this.initialState = null
    this.backupCreated = false
    this.logger.debug(`${ICONS.DEBUG} Git状态管理器已清理`)
  }
}

/**
 * 资源管理器类 - 统一管理所有需要清理的资源
 */
class ReleaseResourceManager {
  constructor() {
    this.resources = new Set()
    this.isCleanedUp = false
    this.logger = console
  }

  /**
   * 添加需要清理的资源
   * @param {object} resource - 资源对象，必须有cleanup方法
   */
  addResource(resource) {
    if (resource && typeof resource.cleanup === 'function') {
      this.resources.add(resource)
    }
    else {
      this.logger.warn(`${ICONS.WARNING} 尝试添加无效资源，资源必须有cleanup方法`)
    }
  }

  /**
   * 清理所有资源
   */
  cleanup() {
    if (this.isCleanedUp)
      return

    this.logger.debug(`${ICONS.DEBUG} 开始清理发布资源...`)

    for (const resource of this.resources) {
      try {
        resource.cleanup()
      }
      catch (error) {
        this.logger.error(`${ICONS.ERROR} 清理资源失败:`, error.message)
      }
    }

    this.resources.clear()
    this.isCleanedUp = true
    this.logger.debug(`${ICONS.DEBUG} 发布资源清理完成`)
  }

  /**
   * 设置进程退出时的自动清理
   */
  setupAutoCleanup() {
    const cleanup = () => this.cleanup()

    process.on('exit', cleanup)
    process.on('SIGINT', cleanup)
    process.on('SIGTERM', cleanup)
    process.on('SIGHUP', cleanup)
    process.on('uncaughtException', (error) => {
      this.logger.error(`${ICONS.ERROR} 未捕获的异常:`, error.message)
      cleanup()
      process.exit(1)
    })
    process.on('unhandledRejection', (reason) => {
      this.logger.error(`${ICONS.ERROR} 未处理的Promise拒绝:`, reason)
      cleanup()
      process.exit(1)
    })
  }
}

/**
 * 发布流程管理器
 */
class ReleaseManager {
  constructor(options = {}) {
    this.execute = options.execute || 'pnpm cm changelog'
    this.push = options.push !== false
    this.signalHandler = options.signalHandler || null
    this.logger = console
    this.state = RELEASE_STATES.IDLE
    this.isCompleted = false
    this.isCancelled = false
  }

  /**
   * 更新发布状态
   * @param {string} newState - 新状态
   */
  setState(newState) {
    this.logger.debug(`${ICONS.DEBUG} 发布状态: ${this.state} -> ${newState}`)
    this.state = newState
  }

  /**
   * 检查Git仓库状态
   * @returns {Promise<boolean>} 是否可以继续发布
   */
  async checkGitStatus() {
    try {
      this.logger.info(`${ICONS.INFO} 正在检查Git仓库状态...`)

      // 检查是否在Git仓库中
      const { success: isGitRepo } = await execCommandSafe('git', ['rev-parse', '--git-dir'])
      if (!isGitRepo) {
        throw new Error('当前目录不是Git仓库')
      }

      // 检查是否有未跟踪的文件
      const { output: untrackedFiles } = await execCommandSafe('git', ['ls-files', '--others', '--exclude-standard'])
      if (untrackedFiles.trim()) {
        this.logger.warn(`${ICONS.WARNING} 发现未跟踪的文件，建议先添加到Git`)
        this.logger.info(`${ICONS.INFO} 未跟踪的文件:\n${untrackedFiles}`)
      }

      // 检查远程仓库连接
      const { success: hasRemote } = await execCommandSafe('git', ['remote', 'get-url', 'origin'])
      if (!hasRemote && this.push) {
        this.logger.warn(`${ICONS.WARNING} 未配置远程仓库，将跳过推送步骤`)
        this.push = false
      }

      this.logger.info(`${ICONS.SUCCESS} Git仓库状态检查完成`)
      return true
    }
    catch (error) {
      this.logger.error(`${ICONS.ERROR} Git仓库状态检查失败:`, error.message)
      return false
    }
  }

  /**
   * 执行版本更新
   * @returns {Promise<boolean>} 是否成功
   */
  async performVersionBump() {
    try {
      this.setState(RELEASE_STATES.BUMPING)
      this.logger.info(`${ICONS.ROCKET} 开始版本更新流程...`)
      this.logger.info(`${ICONS.INFO} 提示: 在版本选择过程中，按 Ctrl+C 可取消操作`)

      await versionBump({
        files: ['**/package.json', '!**/node_modules'],
        execute: this.execute,
        all: true,
        tag: true,
        commit: 'chore(projects): release v%s',
        push: this.push,
        noVerify: true, // 跳过 git hooks 验证，避免冲突
      })

      this.setState(RELEASE_STATES.COMPLETED)
      this.isCompleted = true
      return true
    }
    catch (error) {
      this.setState(RELEASE_STATES.FAILED)
      throw error
    }
  }

  /**
   * 取消发布流程
   * @param {string} reason - 取消原因
   */
  cancel(reason = '用户取消') {
    this.setState(RELEASE_STATES.CANCELLED)
    this.isCancelled = true
    this.logger.info(`${ICONS.WARNING} 发布流程已取消: ${reason}`)
  }
}

/**
 * 发布新版本 - 优化版本
 * 支持多种退出键取消操作，提供版本回退功能和增强的错误处理
 * 默认会推送到远程仓库，可通过 --no-push 禁用推送
 * @param {string} [execute] - 版本更新后执行的命令
 * @param {boolean} [push] - 是否推送到远程仓库
 * @returns {Promise<void>} Promise that resolves when release is complete
 */
export async function release(execute = 'pnpm cm changelog', push = true) {
  // 创建资源管理器
  const resourceManager = new ReleaseResourceManager()
  resourceManager.setupAutoCleanup()

  // 创建Git状态管理器
  const gitStateManager = new GitStateManager()
  resourceManager.addResource(gitStateManager)

  let keyboardListener = null
  let releaseManager = null

  /**
   * 优雅退出处理
   * @param {string} reason - 退出原因
   * @param {boolean} shouldRollback - 是否需要回退
   */
  const gracefulExit = async (reason = '用户取消', shouldRollback = true) => {
    console.log(`\n${ICONS.WARNING} 发布流程已取消 (${reason})`)

    if (releaseManager) {
      releaseManager.cancel(reason)
    }

    // 根据发布状态决定是否需要回退
    if (shouldRollback && gitStateManager.backupCreated && releaseManager && !releaseManager.isCompleted) {
      const needsRollback = [
        RELEASE_STATES.BUMPING,
        RELEASE_STATES.EXECUTING,
        RELEASE_STATES.COMMITTING,
        RELEASE_STATES.TAGGING,
        RELEASE_STATES.PUSHING,
      ].includes(releaseManager.state)

      if (needsRollback) {
        console.log(`${ICONS.ROLLBACK} 正在回退版本更改...`)
        const rollbackSuccess = await gitStateManager.rollbackToInitialState()

        if (rollbackSuccess) {
          console.log(`${ICONS.SUCCESS} 版本回退完成，仓库已恢复到发布前状态`)
        }
        else {
          console.log(`${ICONS.ERROR} 版本回退失败，请手动检查并修复`)
          console.log(`${ICONS.INFO} 建议执行以下命令检查状态:`)
          console.log(`  git status`)
          console.log(`  git log --oneline -5`)
        }
      }
    }

    resourceManager.cleanup()
    process.exit(0)
  }

  /**
   * 设置信号处理
   */
  const setupSignalHandler = () => {
    try {
      // 检查信号处理支持
      if (!ReleaseSignalHandler.isSupported()) {
        console.log(`${ICONS.INFO} 当前环境不支持信号处理`)
        return
      }

      // 创建信号处理器
      keyboardListener = new ReleaseSignalHandler({
        logger: console,
        exitCallback: async (signal) => {
          console.log(`${ICONS.WARNING} 收到中断信号: ${signal}`)
          await gracefulExit(`${signal}信号`)
        },
      })

      // 添加到资源管理器
      resourceManager.addResource(keyboardListener)

      // 启动监听
      const started = keyboardListener.start()
      if (started) {
        console.log(`${ICONS.SUCCESS} 信号处理器启动成功`)
      }
      else {
        console.log(`${ICONS.WARNING} 信号处理器启动失败`)
      }
    }
    catch (error) {
      console.error(`${ICONS.ERROR} 设置信号处理失败:`, error.message)
      console.log(`${ICONS.INFO} 信号处理功能已禁用，但程序将继续运行`)
    }
  }

  // 主执行流程
  try {
    console.log(`${ICONS.ROCKET} 开始发布流程...`)
    console.log(`${ICONS.INFO} 提示: 按 Ctrl+C 可随时取消发布流程`)

    // 设置信号处理
    setupSignalHandler()

    // 创建发布管理器（在信号处理器创建之后）
    releaseManager = new ReleaseManager({ execute, push, signalHandler: keyboardListener })

    // 检查Git仓库状态
    releaseManager.setState(RELEASE_STATES.PREPARING)
    const gitStatusOk = await releaseManager.checkGitStatus()
    if (!gitStatusOk) {
      throw new Error('Git仓库状态检查失败，无法继续发布')
    }

    // 保存当前Git状态
    const stateBackupOk = await gitStateManager.saveCurrentState()
    if (!stateBackupOk) {
      console.log(`${ICONS.WARNING} Git状态备份失败，将继续发布但无法自动回退`)
    }

    // 执行版本更新
    const versionBumpOk = await releaseManager.performVersionBump()
    if (!versionBumpOk) {
      throw new Error('版本更新失败')
    }

    console.log(`${ICONS.SUCCESS} 发布完成！`)

    // 显示发布信息
    try {
      const { output: latestTag } = await execCommandSafe('git', ['describe', '--tags', '--abbrev=0'])
      if (latestTag) {
        console.log(`${ICONS.INFO} 新版本标签: ${latestTag}`)
      }

      const { output: currentCommit } = await execCommandSafe('git', ['rev-parse', '--short', 'HEAD'])
      if (currentCommit) {
        console.log(`${ICONS.INFO} 当前提交: ${currentCommit}`)
      }
    }
    catch {
      // 忽略获取版本信息的错误
    }
  }
  catch (error) {
    // 增强的错误处理
    const errorMessage = error.message || '未知错误'
    const isCancelled = errorMessage.includes('cancelled')
      || errorMessage.includes('canceled')
      || errorMessage.includes('aborted')
      || (releaseManager && releaseManager.isCancelled)

    if (isCancelled) {
      await gracefulExit('操作被取消', true)
    }
    else {
      console.error(`${ICONS.ERROR} 发布失败: ${errorMessage}`)

      // 详细错误信息
      if (error.stack) {
        console.error(`${ICONS.DEBUG} 错误详情:`, error.stack)
      }

      // 根据错误类型提供建议
      if (errorMessage.includes('git')) {
        console.log(`${ICONS.INFO} 建议检查Git配置和网络连接`)
      }
      else if (errorMessage.includes('permission')) {
        console.log(`${ICONS.INFO} 建议检查文件权限和访问权限`)
      }
      else if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
        console.log(`${ICONS.INFO} 建议检查网络连接并重试`)
      }

      // 询问是否需要回退
      if (gitStateManager.backupCreated && releaseManager && !releaseManager.isCompleted) {
        console.log(`${ICONS.INFO} 检测到发布过程中出现错误`)
        await gracefulExit('发布失败', true)
      }
    }
  }
  finally {
    // 确保清理所有资源
    resourceManager.cleanup()
  }
}
