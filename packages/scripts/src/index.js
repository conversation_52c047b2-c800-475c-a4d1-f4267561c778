import { readFileSync } from 'node:fs'
import { dirname, join } from 'node:path'
import { fileURLToPath } from 'node:url'
import cac from 'cac'
import { blue, lightGreen } from 'kolorist'
import { buildEnv, checkPnpm, cleanup, genChangelog, gitCommit, gitCommitVerify, release, updatePkg } from './commands/index.js'
import { loadCliOptions } from './config/index.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const { version } = JSON.parse(readFileSync(join(__dirname, '../package.json'), 'utf8'))

/**
 * Setup CLI application
 * @returns {Promise<void>} Promise that resolves when CLI is set up
 */
export async function setupCli() {
  const cliOptions = await loadCliOptions()

  const cli = cac(blue('cm-scripts'))

  cli
    .version(lightGreen(version))
    .option(
      '-e, --execute [command]',
      'Execute additional command after bumping and before git commit. Defaults to \'pnpm cm changelog\'',
    )
    .option('-p, --push', 'Indicates whether to push the git commit and tag')
    .option('-t, --total', 'Generate changelog by total tags')
    .option(
      '-c, --cleanupDir <dir>',
      'The glob pattern of dirs to cleanup, If not set, it will use the default value, Multiple values use "," to separate them',
    )
    .option('-l, --lang <lang>', 'display lang of cli', { default: 'en-us', type: [String] })
    .help()

  const commands = {
    'check-pnpm': {
      desc: 'check and enforce pnpm as package manager',
      action: () => checkPnpm(),
    },
    'build-env': {
      desc: 'interactive environment selection for building',
      action: () => buildEnv(),
    },
    'cleanup': {
      desc: 'delete dirs: node_modules, dist',
      action: () => cleanup(cliOptions.cleanupDirs),
    },
    'update-pkg': {
      desc: 'update package.json dependencies versions',
      action: () => updatePkg(cliOptions.ncuCommandArgs),
    },
    'git-commit': {
      desc: 'git commit, generate commit message which match Conventional Commits standard',
      action: args => gitCommit(args?.lang),
    },
    'git-commit-verify': {
      desc: 'verify git commit message, make sure it match Conventional Commits standard',
      action: args => gitCommitVerify(args?.lang, cliOptions.gitCommitVerifyIgnores),
    },
    'changelog': {
      desc: 'generate changelog',
      action: args => genChangelog(cliOptions.changelogOptions, args?.total),
    },
    'release': {
      desc: 'release: update version, generate changelog, commit code',
      action: (args) => {
        // 如果没有明确指定 --push 或 --no-push，使用默认值 true
        const shouldPush = args?.push ?? true
        return release(args?.execute, shouldPush)
      },
    },
  }

  for (const [command, { desc, action }] of Object.entries(commands)) {
    cli.command(command, lightGreen(desc)).action(action)
  }

  cli.parse()
}

setupCli()
