/**
 * 增强的错误处理系统
 * 提供错误捕获、分类、上报和用户友好的错误提示
 */

// 错误类型枚举
export const ERROR_TYPES = {
  JAVASCRIPT: 'javascript',
  NETWORK: 'network',
  RESOURCE: 'resource',
  PROMISE: 'promise',
  VUE: 'vue',
  CUSTOM: 'custom',
}

// 错误级别枚举
export const ERROR_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
}

// 用户友好的错误消息映射
const ERROR_MESSAGES = {
  InternalError: '系统内部错误，请稍后重试',
  ReferenceError: '页面加载异常，请刷新页面',
  TypeError: '数据处理异常，请检查输入',
  RangeError: '参数超出有效范围',
  SyntaxError: '数据格式错误',
  EvalError: '脚本执行错误',
  URIError: '地址格式错误',
  NetworkError: '网络连接异常，请检查网络',
  TimeoutError: '请求超时，请稍后重试',
  default: '操作失败，请稍后重试',
}

/**
 * 错误处理器类
 */
export class ErrorHandler {
  constructor(options = {}) {
    this.options = {
      enableConsoleLog: true,
      enableUserNotification: true,
      enableErrorReporting: false,
      maxErrorCount: 10,
      reportUrl: '',
      ...options,
    }
    
    this.errorQueue = []
    this.errorCount = 0
    this.init()
  }

  /**
   * 初始化错误处理器
   */
  init() {
    // 捕获 JavaScript 错误
    window.addEventListener('error', this.handleJavaScriptError.bind(this))
    
    // 捕获 Promise 未处理的 rejection
    window.addEventListener('unhandledrejection', this.handlePromiseError.bind(this))
    
    // 捕获资源加载错误
    window.addEventListener('error', this.handleResourceError.bind(this), true)
  }

  /**
   * 处理 JavaScript 错误
   */
  handleJavaScriptError(event) {
    const error = {
      type: ERROR_TYPES.JAVASCRIPT,
      level: this.getErrorLevel(event.error),
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error?.stack,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    }

    this.processError(error)
  }

  /**
   * 处理 Promise 错误
   */
  handlePromiseError(event) {
    const error = {
      type: ERROR_TYPES.PROMISE,
      level: ERROR_LEVELS.MEDIUM,
      message: event.reason?.message || 'Promise rejection',
      stack: event.reason?.stack,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    }

    this.processError(error)
    event.preventDefault() // 阻止控制台输出
  }

  /**
   * 处理资源加载错误
   */
  handleResourceError(event) {
    if (event.target !== window) {
      const error = {
        type: ERROR_TYPES.RESOURCE,
        level: ERROR_LEVELS.LOW,
        message: `Resource load failed: ${event.target.src || event.target.href}`,
        element: event.target.tagName,
        source: event.target.src || event.target.href,
        timestamp: Date.now(),
        url: window.location.href,
      }

      this.processError(error)
    }
  }

  /**
   * 处理 Vue 错误
   */
  handleVueError(error, vm, info) {
    const errorInfo = {
      type: ERROR_TYPES.VUE,
      level: this.getErrorLevel(error),
      message: error.message,
      stack: error.stack,
      componentName: vm?.$options?.name || 'Unknown',
      errorInfo: info,
      timestamp: Date.now(),
      url: window.location.href,
    }

    this.processError(errorInfo)
  }

  /**
   * 手动报告错误
   */
  reportError(error, level = ERROR_LEVELS.MEDIUM) {
    const errorInfo = {
      type: ERROR_TYPES.CUSTOM,
      level,
      message: error.message || error,
      stack: error.stack,
      timestamp: Date.now(),
      url: window.location.href,
    }

    this.processError(errorInfo)
  }

  /**
   * 处理错误
   */
  processError(error) {
    // 防止错误过多
    if (this.errorCount >= this.options.maxErrorCount) {
      return
    }

    this.errorCount++
    this.errorQueue.push(error)

    // 控制台日志
    if (this.options.enableConsoleLog) {
      console.error('[ErrorHandler]', error)
    }

    // 用户通知
    if (this.options.enableUserNotification && this.shouldNotifyUser(error)) {
      this.notifyUser(error)
    }

    // 错误上报
    if (this.options.enableErrorReporting) {
      this.reportToServer(error)
    }
  }

  /**
   * 获取错误级别
   */
  getErrorLevel(error) {
    if (!error) return ERROR_LEVELS.LOW

    // 根据错误类型判断级别
    if (error.name === 'TypeError' || error.name === 'ReferenceError') {
      return ERROR_LEVELS.HIGH
    }
    
    if (error.name === 'NetworkError') {
      return ERROR_LEVELS.MEDIUM
    }

    return ERROR_LEVELS.LOW
  }

  /**
   * 判断是否需要通知用户
   */
  shouldNotifyUser(error) {
    // 只有中高级别的错误才通知用户
    return [ERROR_LEVELS.MEDIUM, ERROR_LEVELS.HIGH, ERROR_LEVELS.CRITICAL].includes(error.level)
  }

  /**
   * 通知用户
   */
  notifyUser(error) {
    const message = ERROR_MESSAGES[error.message] || ERROR_MESSAGES.default
    
    // 这里可以集成具体的 UI 库通知组件
    if (typeof window !== 'undefined' && window.showToast) {
      window.showToast({
        type: 'error',
        message,
        duration: 3000,
      })
    } else {
      console.warn('Toast notification not available:', message)
    }
  }

  /**
   * 上报错误到服务器
   */
  async reportToServer(error) {
    if (!this.options.reportUrl) return

    try {
      await fetch(this.options.reportUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(error),
      })
    } catch (reportError) {
      console.error('Failed to report error:', reportError)
    }
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    const stats = {
      total: this.errorQueue.length,
      byType: {},
      byLevel: {},
    }

    this.errorQueue.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1
      stats.byLevel[error.level] = (stats.byLevel[error.level] || 0) + 1
    })

    return stats
  }

  /**
   * 清空错误队列
   */
  clearErrors() {
    this.errorQueue = []
    this.errorCount = 0
  }
}

// 创建默认实例
export const errorHandler = new ErrorHandler()

// 导出 Vue 错误处理函数
export const createVueErrorHandler = (options = {}) => {
  const handler = new ErrorHandler(options)
  return (error, vm, info) => handler.handleVueError(error, vm, info)
}

export default errorHandler
