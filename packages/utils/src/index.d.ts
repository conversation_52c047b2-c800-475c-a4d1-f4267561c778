// @cm/utils 类型声明文件
// 通用工具库，提供加密、存储管理、深拷贝和树结构处理功能

import mitt from 'mitt';

// ===== 加密工具类型 =====
export interface CryptoStatic {
  cryptoKey: string;
  aesKey: string;
  desKey: string;
  
  /**
   * 默认 AES 加密方法
   */
  encrypt(data: string): string;
  
  /**
   * 默认 AES 解密方法  
   */
  decrypt(data: string): string;
  
  /**
   * AES 加密
   */
  encryptAES(data: string, key: string): string;
  
  /**
   * AES 解密
   */
  decryptAES(data: string, key: string): string;
  
  /**
   * DES 加密
   */
  encryptDES(data: string, key?: string): string;
  
  /**
   * DES 解密
   */
  decryptDES(data: string, key?: string): string;
  
  /**
   * 生成随机密钥
   */
  generateKey(length?: number): string;
}

export const crypto: CryptoStatic;

// ===== 存储管理器类型 =====
export interface StorageManager {
  /**
   * 获取存储内容（带过期校验）
   */
  get<T = any>(key: string): Promise<T | null>;
  get<T = any>(key: string, defaultValue: T | (() => T)): Promise<T>;
  get<T = any>(key: string, defaultValue: T | (() => T), options: { autoSave?: boolean; bypassCache?: boolean }): Promise<T>;
  
  /**
   * 存储数据
   */
  set<T = any>(key: string, value: T, expire?: number | null): Promise<T | null>;
  
  /**
   * 移除指定键的数据
   */
  remove(key: string): Promise<boolean>;
  
  /**
   * 清空所有存储数据
   */
  clear(): Promise<boolean>;
  
  /**
   * 获取所有存储键名
   */
  keys(): Promise<string[]>;
  
  /**
   * 遍历所有存储项
   */
  iterate(iterator: (value: any, key: string, index: number) => void): Promise<void>;
  
  /**
   * 批量获取多个键的值
   */
  getBatch<T = any>(keys: string[]): Promise<Record<string, T>>;
  
  /**
   * 批量设置多个键值
   */
  setBatch<T = any>(entries: Record<string, T>, expire?: number | null): Promise<boolean>;
  
  /**
   * 检查键是否存在且未过期
   */
  has(key: string): Promise<boolean>;
  
  /**
   * 获取存储项的元信息
   */
  getMetadata(key: string): Promise<{
    exists: boolean;
    updated: number | null;
    expires: number | null;
    remainingTime: number | null;
  } | null>;
  
  /**
   * 控制缓存功能
   */
  setCacheEnabled(enabled: boolean): void;
  
  /**
   * 清理过期的存储项
   */
  cleanExpiredItems(): Promise<number>;
}

export const storage: StorageManager;
export default storage;

// ===== 深拷贝工具类型 =====
/**
 * 高性能深拷贝函数
 */
export function cloneDeep<T>(value: T): T;

/**
 * 浅拷贝函数
 */
export function shallowClone<T>(value: T): T;

/**
 * 深度相等比较
 */
export function isDeepEqual<T>(a: T, b: T): boolean;

// ===== 验证工具类型 =====
export interface ValidatorStatic {
  /**
   * 判断值是否为空
   */
  isEmpty(val: any): boolean;
  
  /**
   * 判断值是否不为空
   */
  isNotEmpty(val: any): boolean;
  
  /**
   * 验证是否为有效的 JSON
   */
  isValidJSON(val: any): boolean;
  
  /**
   * 验证是否为有效的邮箱地址
   */
  isValidEmail(email: string): boolean;
  
  /**
   * 验证是否为有效的手机号
   */
  isValidPhone(phone: string): boolean;
}

export const validators: ValidatorStatic;

// 导出验证函数
export function isEmpty(val: any): boolean;
export function isNotEmpty(val: any): boolean;
export function isValidJSON(val: any): boolean;
export function isValidEmail(email: string): boolean;
export function isValidPhone(phone: string): boolean;

// ===== DOM 工具类型 =====
export interface LoadExternalOptions {
  id?: string;
  onLoad?: () => void;
  onError?: (error: Event) => void;
}

export interface LoadExternalJSOptions extends LoadExternalOptions {
  async?: boolean;
  defer?: boolean;
  type?: string;
}

export interface SerializeOptions {
  separator?: string;
  encode?: boolean;
  ignoreEmpty?: boolean;
}

export interface DomUtilsStatic {
  /**
   * 动态引入 CSS 文件
   */
  loadExternalCSS(url: string, options?: LoadExternalOptions): HTMLLinkElement;
  
  /**
   * 动态引入 JavaScript 文件
   */
  loadExternalJS(url: string, options?: LoadExternalJSOptions): HTMLScriptElement;
  
  /**
   * 复制文本到剪贴板
   */
  copyToClipboard(text: string): Promise<boolean>;
  
  /**
   * 序列化对象为查询字符串
   */
  serialize(obj: Record<string, any>, options?: SerializeOptions): string;
}

export const domUtils: DomUtilsStatic;

// 导出 DOM 工具函数
export function loadExternalCSS(url: string, options?: LoadExternalOptions): HTMLLinkElement;
export function copyToClipboard(text: string): Promise<boolean>;
export function serialize(obj: Record<string, any>, options?: SerializeOptions): string;

// ===== 树结构工具类型 =====
export interface TreeNode {
  id: string | number;
  parentId?: string | number | null;
  children?: TreeNode[];
  [key: string]: any;
}

export interface TreeOptions {
  idField?: string;
  parentIdField?: string;
  childrenField?: string;
  rootValue?: any;
  createChildren?: boolean;
}

export interface TreeStats {
  totalNodes: number;
  maxDepth: number;
  leafNodes: number;
  avgChildrenCount: number;
}

// 树结构构建器
export function buildTreeFromFlat<T extends TreeNode>(
  flatArray: T[],
  options?: TreeOptions
): T[];

export function buildHierarchyTree<T extends TreeNode>(
  flatArray: T[],
  options?: TreeOptions
): T[];

export function optimizeTreeStructure<T extends TreeNode>(
  tree: T[],
  options?: TreeOptions
): T[];

export function handleTree<T extends TreeNode>(
  tree: T[],
  handler: (node: T) => T,
  options?: TreeOptions
): T[];

// 树结构遍历器
export function traverseDepthFirst<T extends TreeNode>(
  tree: T[],
  callback: (node: T, parent?: T, depth?: number) => void | boolean,
  options?: TreeOptions
): void;

export function traverseBreadthFirst<T extends TreeNode>(
  tree: T[],
  callback: (node: T, level: number) => void | boolean,
  options?: TreeOptions
): void;

export function extractFieldValues<T extends TreeNode>(
  tree: T[],
  fieldName: string,
  options?: TreeOptions
): any[];

export function extractPathList<T extends TreeNode>(
  tree: T[],
  options?: TreeOptions & { separator?: string }
): string[];

export function getTreeStats<T extends TreeNode>(
  tree: T[],
  options?: TreeOptions
): TreeStats;

// 树结构操作器
export function findNode<T extends TreeNode>(
  tree: T[],
  predicate: (node: T) => boolean,
  options?: TreeOptions
): T | null;

export function getNodeByUniqueId<T extends TreeNode>(
  tree: T[],
  id: string | number,
  options?: TreeOptions
): T | null;

export function filterTree<T extends TreeNode>(
  tree: T[],
  predicate: (node: T) => boolean,
  options?: TreeOptions
): T[];

export function mapTree<T extends TreeNode, R extends TreeNode>(
  tree: T[],
  mapper: (node: T) => R,
  options?: TreeOptions
): R[];

export function appendFieldByUniqueId<T extends TreeNode>(
  tree: T[],
  id: string | number,
  field: string,
  value: any,
  options?: TreeOptions
): T[];

export function getNodePath<T extends TreeNode>(
  tree: T[],
  id: string | number,
  options?: TreeOptions & { separator?: string }
): string | null;

// 兼容性别名
export const deleteChildren: typeof optimizeTreeStructure;

// ===== 事件发射器类型 =====
export const mitter: mitt.Emitter<Record<string, any>>;

// ===== 错误处理工具类型 =====
export interface ErrorHandlerOptions {
  showToast?: boolean;
  logError?: boolean;
  throwError?: boolean;
  customMessage?: string;
}

export interface ErrorHandlerResult {
  success: boolean;
  error?: Error;
  message?: string;
}

export function handleError(
  error: Error | unknown,
  options?: ErrorHandlerOptions
): ErrorHandlerResult;

// ===== 主入口默认导出（向后兼容） =====
export { storage as default } from './storage';
