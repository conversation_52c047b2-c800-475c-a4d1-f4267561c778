/**
 * 树操作器模块
 * 提供查找、过滤、转换、修改树节点的功能
 */

import { traverseDepthFirst } from './traversal.js'

/**
 * 根据条件查找节点（深度优先）
 * @param {Array|Object} tree - 树结构数据
 * @param {Function} predicate - 查找条件函数 (node) => boolean
 * @param {Object} [options={}] - 配置选项
 * @param {string} [options.childrenKey='children'] - 子节点字段名
 * @returns {Object|null} 找到的节点或null
 */
export function findNode(tree, predicate, options = {}) {
  if (!tree || typeof predicate !== 'function') {
    return null
  }

  let foundNode = null

  try {
    traverseDepthFirst(tree, (node) => {
      if (predicate(node)) {
        foundNode = node
        throw new Error('Found') // 用于提前退出遍历
      }
    }, options)
  } catch (error) {
    if (error.message !== 'Found') {
      console.error('Error in findNode:', error)
    }
  }

  return foundNode
}

/**
 * 根据唯一ID查找节点（兼容原有API）
 * @param {Array} tree - 树结构数据
 * @param {string|number} uniqueId - 唯一ID
 * @param {Object} [options={}] - 配置选项
 * @param {string} [options.idKey='uniqueId'] - ID字段名
 * @param {string} [options.childrenKey='children'] - 子节点字段名
 * @returns {Object|null} 找到的节点或null
 */
export function getNodeByUniqueId(tree, uniqueId, options = {}) {
  const config = {
    idKey: options.idKey || 'uniqueId',
    childrenKey: options.childrenKey || 'children',
  }

  return findNode(tree, (node) => node[config.idKey] === uniqueId, {
    childrenKey: config.childrenKey,
  })
}

/**
 * 过滤树结构，保留满足条件的节点及其祖先和后代
 * @param {Array|Object} tree - 树结构数据
 * @param {Function} predicate - 过滤条件函数 (node) => boolean
 * @param {Object} [options={}] - 配置选项
 * @param {string} [options.childrenKey='children'] - 子节点字段名
 * @param {boolean} [options.keepAncestors=true] - 是否保留祖先节点
 * @param {boolean} [options.keepDescendants=true] - 是否保留后代节点
 * @returns {Array} 过滤后的树
 */
export function filterTree(tree, predicate, options = {}) {
  if (!tree || typeof predicate !== 'function') {
    return Array.isArray(tree) ? [] : null
  }

  const config = {
    childrenKey: options.childrenKey || 'children',
    keepAncestors: options.keepAncestors !== false,
    keepDescendants: options.keepDescendants !== false,
  }

  const isArray = Array.isArray(tree)
  const nodes = isArray ? tree : [tree]

  function filterNode(node) {
    if (!node) return null

    const children = node[config.childrenKey]
    let filteredChildren = []

    // 递归过滤子节点
    if (children && Array.isArray(children)) {
      filteredChildren = children
        .map(child => filterNode(child))
        .filter(child => child !== null)
    }

    // 检查当前节点是否满足条件
    const nodeMatches = predicate(node)

    // 如果节点匹配或有匹配的子节点，保留该节点
    if (nodeMatches || filteredChildren.length > 0) {
      const newNode = { ...node }

      if (filteredChildren.length > 0) {
        newNode[config.childrenKey] = filteredChildren
      } else if (newNode[config.childrenKey]) {
        // 如果没有匹配的子节点，删除children属性
        delete newNode[config.childrenKey]
      }

      return newNode
    }

    return null
  }

  const result = nodes
    .map(node => filterNode(node))
    .filter(node => node !== null)

  return isArray ? result : (result.length > 0 ? result[0] : null)
}

/**
 * 转换树结构，对每个节点应用转换函数
 * @param {Array|Object} tree - 树结构数据
 * @param {Function} mapper - 转换函数 (node, level, path) => newNode
 * @param {Object} [options={}] - 配置选项
 * @param {string} [options.childrenKey='children'] - 子节点字段名
 * @returns {Array|Object} 转换后的树
 */
export function mapTree(tree, mapper, options = {}) {
  if (!tree || typeof mapper !== 'function') {
    return tree
  }

  const config = {
    childrenKey: options.childrenKey || 'children',
  }

  const isArray = Array.isArray(tree)
  const nodes = isArray ? tree : [tree]

  function mapNode(node, level = 0, path = []) {
    if (!node) return null

    // 应用转换函数
    const mappedNode = mapper(node, level, path)

    // 处理子节点
    const children = node[config.childrenKey]
    if (children && Array.isArray(children) && children.length > 0) {
      const mappedChildren = children.map((child, index) =>
        mapNode(child, level + 1, [...path, index])
      ).filter(child => child !== null)

      if (mappedChildren.length > 0) {
        mappedNode[config.childrenKey] = mappedChildren
      }
    }

    return mappedNode
  }

  const result = nodes.map((node, index) =>
    mapNode(node, 0, [index])
  ).filter(node => node !== null)

  return isArray ? result : (result.length > 0 ? result[0] : null)
}

/**
 * 向指定节点追加字段（兼容原有API）
 * @param {Array} tree - 树结构数据
 * @param {string|number} uniqueId - 唯一ID
 * @param {Object} fields - 要追加的字段
 * @param {Object} [options={}] - 配置选项
 * @param {string} [options.idKey='uniqueId'] - ID字段名
 * @param {string} [options.childrenKey='children'] - 子节点字段名
 * @returns {Array} 修改后的树
 */
export function appendFieldByUniqueId(tree, uniqueId, fields, options = {}) {
  if (!Array.isArray(tree) || !fields || typeof fields !== 'object') {
    console.warn('Invalid parameters for appendFieldByUniqueId')
    return tree
  }

  const config = {
    idKey: options.idKey || 'uniqueId',
    childrenKey: options.childrenKey || 'children',
  }

  function updateNode(nodes) {
    for (const node of nodes) {
      if (node[config.idKey] === uniqueId) {
        Object.assign(node, fields)
        return true // 找到并更新了节点
      }

      const children = node[config.childrenKey]
      if (children && Array.isArray(children) && children.length > 0) {
        if (updateNode(children)) {
          return true // 在子节点中找到并更新了
        }
      }
    }
    return false
  }

  updateNode(tree)
  return tree
}

/**
 * 获取节点的完整路径
 * @param {Array|Object} tree - 树结构数据
 * @param {string|number} targetId - 目标节点ID
 * @param {Object} [options={}] - 配置选项
 * @param {string} [options.idKey='id'] - ID字段名
 * @param {string} [options.childrenKey='children'] - 子节点字段名
 * @param {string} [options.labelKey='name'] - 标签字段名
 * @returns {Array} 路径数组
 */
export function getNodePath(tree, targetId, options = {}) {
  if (!tree || targetId == null) {
    return []
  }

  const config = {
    idKey: options.idKey || 'id',
    childrenKey: options.childrenKey || 'children',
    labelKey: options.labelKey || 'name',
  }

  const path = []

  function findPath(nodes, currentPath = []) {
    if (!Array.isArray(nodes)) {
      nodes = [nodes]
    }

    for (const node of nodes) {
      if (!node) continue

      const newPath = [...currentPath, {
        id: node[config.idKey],
        label: node[config.labelKey] || node[config.idKey],
        node: node,
      }]

      if (node[config.idKey] === targetId) {
        path.push(...newPath)
        return true
      }

      const children = node[config.childrenKey]
      if (children && Array.isArray(children) && children.length > 0) {
        if (findPath(children, newPath)) {
          return true
        }
      }
    }

    return false
  }

  findPath(tree)
  return path
}

// 默认导出主要函数
export default {
  findNode,
  getNodeByUniqueId,
  filterTree,
  mapTree,
  appendFieldByUniqueId,
  getNodePath,
}
