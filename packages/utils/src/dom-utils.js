/**
 * DOM 工具模块
 * 提供 DOM 操作和浏览器环境相关的工具函数
 */

/**
 * 动态引入 CSS 文件
 * @param {string} url - 需引入的 CSS 地址
 * @param {Object} options - 配置选项
 * @param {string} [options.id] - link 元素的 id
 * @param {Function} [options.onLoad] - 加载完成回调
 * @param {Function} [options.onError] - 加载失败回调
 * @returns {HTMLLinkElement} 创建的 link 元素
 */
export function loadExternalCSS(url, options = {}) {
  const { id, onLoad, onError } = options

  // 检查是否已经加载过
  if (id && document.getElementById(id)) {
    console.warn(`CSS with id "${id}" already loaded`)
    return document.getElementById(id)
  }

  const link = document.createElement('link')
  link.href = url
  link.rel = 'stylesheet'
  link.type = 'text/css'

  if (id) {
    link.id = id
  }

  if (onLoad) {
    link.onload = onLoad
  }

  if (onError) {
    link.onerror = onError
  }

  document.head.appendChild(link)
  return link
}

/**
 * 动态引入 JavaScript 文件
 * @param {string} url - 需引入的 JS 地址
 * @param {Object} options - 配置选项
 * @param {string} [options.id] - script 元素的 id
 * @param {boolean} [options.async=true] - 是否异步加载
 * @param {Function} [options.onLoad] - 加载完成回调
 * @param {Function} [options.onError] - 加载失败回调
 * @returns {HTMLScriptElement} 创建的 script 元素
 */
export function loadExternalJS(url, options = {}) {
  const { id, async = true, onLoad, onError } = options

  // 检查是否已经加载过
  if (id && document.getElementById(id)) {
    console.warn(`Script with id "${id}" already loaded`)
    return document.getElementById(id)
  }

  const script = document.createElement('script')
  script.src = url
  script.async = async

  if (id) {
    script.id = id
  }

  if (onLoad) {
    script.onload = onLoad
  }

  if (onError) {
    script.onerror = onError
  }

  document.head.appendChild(script)
  return script
}

/**
 * 表单序列化为 URL 参数
 * @param {Object} data - 需要格式化的数据
 * @param {Object} options - 配置选项
 * @param {boolean} [options.skipEmpty=false] - 是否跳过空值
 * @returns {string} URL 参数字符串
 */
export function serialize(data, options = {}) {
  if (!data || typeof data !== 'object') {
    return ''
  }

  const { skipEmpty = false } = options
  const params = new URLSearchParams()

  Object.entries(data).forEach(([key, value]) => {
    // 跳过空值（如果配置了 skipEmpty）
    if (skipEmpty && (value === '' || value == null)) {
      return
    }

    // 处理数组
    if (Array.isArray(value)) {
      value.forEach(item => params.append(key, String(item)))
    } else {
      params.append(key, String(value))
    }
  })

  return params.toString()
}

/**
 * 解析 URL 参数为对象
 * @param {string} [url] - URL 字符串，默认使用当前页面 URL
 * @returns {Object} 参数对象
 */
export function parseURLParams(url) {
  const targetURL = url || window.location.href
  const urlObj = new URL(targetURL)
  const params = {}

  urlObj.searchParams.forEach((value, key) => {
    // 处理重复的参数名（转为数组）
    if (params[key]) {
      if (Array.isArray(params[key])) {
        params[key].push(value)
      } else {
        params[key] = [params[key], value]
      }
    } else {
      params[key] = value
    }
  })

  return params
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @returns {Promise<boolean>} 复制是否成功
 */
export async function copyToClipboard(text) {
  try {
    // 优先使用现代 API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text)
      return true
    }

    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    textArea.style.position = 'fixed'
    textArea.style.left = '-999999px'
    textArea.style.top = '-999999px'
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()

    const successful = document.execCommand('copy')
    document.body.removeChild(textArea)
    return successful
  } catch (error) {
    console.error('复制到剪贴板失败:', error)
    return false
  }
}

/**
 * 获取元素的位置信息
 * @param {HTMLElement} element - DOM 元素
 * @returns {Object} 位置信息
 */
export function getElementPosition(element) {
  if (!element) return null

  const rect = element.getBoundingClientRect()
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft

  return {
    top: rect.top + scrollTop,
    left: rect.left + scrollLeft,
    right: rect.right + scrollLeft,
    bottom: rect.bottom + scrollTop,
    width: rect.width,
    height: rect.height,
    x: rect.x + scrollLeft,
    y: rect.y + scrollTop,
  }
}

/**
 * 检查元素是否在视口中
 * @param {HTMLElement} element - DOM 元素
 * @param {number} [threshold=0] - 阈值（0-1）
 * @returns {boolean} 是否在视口中
 */
export function isElementInViewport(element, threshold = 0) {
  if (!element) return false

  const rect = element.getBoundingClientRect()
  const windowHeight = window.innerHeight || document.documentElement.clientHeight
  const windowWidth = window.innerWidth || document.documentElement.clientWidth

  const vertInView = (rect.top <= windowHeight * (1 - threshold)) && ((rect.top + rect.height) >= windowHeight * threshold)
  const horInView = (rect.left <= windowWidth * (1 - threshold)) && ((rect.left + rect.width) >= windowWidth * threshold)

  return vertInView && horInView
}

// 默认导出
export default {
  loadExternalCSS,
  loadExternalJS,
  serialize,
  parseURLParams,
  copyToClipboard,
  getElementPosition,
  isElementInViewport,
}
