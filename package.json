{"name": "ssw-app", "type": "module", "version": "0.0.1", "private": true, "description": "Vue 3 + Vite + Tailwind CSS Monorepo 项目，基于 pnpm workspace 架构", "keywords": ["vue3", "vite", "tailwindcss", "monorepo", "pnpm", "mobile", "vant"], "engines": {"node": ">=20.19.0", "pnpm": ">=9.0.0"}, "scripts": {"preinstall": "cm check-pnpm", "dev": "NODE_OPTIONS=--max-old-space-size=8192 vite --host", "build": "cm build-env", "build:stage": "rimraf dist && NODE_OPTIONS=--max-old-space-size=8192 vite build --mode stage", "build:production": "rimraf dist && NODE_OPTIONS=--max-old-space-size=8192 vite build --mode production", "report": "rimraf dist && vite build", "preview": "vite preview", "lint-code": "oxlint -c .oxlint<PERSON>.json --ignore-path=.oxlint<PERSON><PERSON> --deny-warnings", "cleanup": "cm cleanup", "update-pkg": "cm update-pkg", "commit": "cm git-commit -l=zh-cn", "release": "cm release"}, "dependencies": {"@cm/alova": "workspace:*", "@cm/utils": "workspace:*", "dayjs": "^1.11.13", "echarts": "^6.0.0", "js-base64": "^3.7.8", "js-md5": "^0.8.3", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.5.0", "qs": "^6.14.0", "secure-ls": "^2.0.0", "sm-crypto": "^0.3.13", "vant": "^4.9.21", "vconsole": "^3.15.1", "vue": "3.6.0-alpha.1", "vue-router": "^4.5.1"}, "devDependencies": {"@cm/scripts": "workspace:*", "@iconify/vue": "^5.0.0", "@prettier/plugin-oxc": "^0.0.4", "@tailwindcss/vite": "^4.1.12", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^6.0.1", "@vitejs/plugin-vue-jsx": "^5.0.1", "autoprefixer": "^10.4.21", "oxlint": "^1.12.0", "postcss": "^8.5.6", "postcss-mobile-forever": "^5.0.0", "prettier": "^3.6.2", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^6.0.3", "sass": "^1.90.0", "tailwindcss": "^4.1.12", "unplugin-auto-import": "^20.0.0", "unplugin-vue-components": "^29.0.0", "unplugin-vue-router": "^0.15.0", "vite": "npm:rolldown-vite@^7.1.4", "vite-plugin-compression": "^0.5.1", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-vconsole": "^2.1.1", "vite-plugin-vue-devtools": "^8.0.0", "vite-plugin-vue-layouts-next": "^1.0.0"}, "lint-staged": {"*.{js,jsx,ts,tsx,vue}": ["oxlint --fix"], "*.{css,scss,less,html,json,md}": ["prettier --write"]}}