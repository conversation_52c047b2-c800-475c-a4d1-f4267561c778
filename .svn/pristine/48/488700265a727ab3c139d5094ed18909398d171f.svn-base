import { mitter } from "@cm/utils";
import storage from "@cm/utils/storage";
import { createServerTokenAuthentication } from "alova/client";
import { refreshTokenAPI } from "@/api/user.js";
import { accessTokenKey, refreshToken<PERSON>ey, tokenHeader, tokenType } from "@/config/website.js";

/**
 * 创建Token认证拦截器
 */
export const { onAuthRequired, onResponseRefreshToken } = createServerTokenAuthentication({
  // Token刷新策略，响应时检测是否需要刷新token
  refreshTokenOnSuccess: {
    // 判断token是否过期（401状态码）
    isExpired: (response) => {
      return response.status === 401;
    },
    // 当token过期时刷新token
    handler: async () => {
      try {
        const refreshToken = await storage.get(refreshTokenKey);
        const res = await refreshTokenAPI(refreshToken);
        if (res?.success !== false) {
          await storage.set(accessTokenKey, Reflect.get(res, accessTokenKey), 24 * 60 * 60);
          await storage.set(refreshTokenKey, Reflect.get(res, refreshTokenKey), 24 * 60 * 60 * 2);
        } else {
          mitter.emit("ROUTER_LOGIN_EXPIRED", () => {});
          throw new Error(res.error_description || "刷新Token失败");
        }
      } catch (error) {
        console.error("Token刷新失败:", error);
        mitter.emit("ROUTER_LOGIN_EXPIRED", () => {});
        throw error;
      }
    },
  },
  refreshTokenOnError: {
    // 响应时触发，可获取到error和method，并返回boolean表示token是否过期
    // 当服务端返回401时，表示token过期
    isExpired: (error, method) => {
      void method;
      return error.status === 401;
    },

    // 当token过期时触发，在此函数中触发刷新token
    handler: async (error, method) => {
      void method;
      void error;
    },
  },
  // 登录成功时保存token
  login: async (response) => {
    if (response.status === 200) {
      const res = await response.clone().json();
      if (res.access_token) {
        storage.set(accessTokenKey, Reflect.get(res, accessTokenKey), 24 * 60 * 60);
        storage.set(refreshTokenKey, Reflect.get(res, refreshTokenKey), 24 * 60 * 60 * 2);
      }
    }
  },
  // 请求时附加token到请求头
  assignToken: async (method) => {
    try {
      const token = await storage.get(accessTokenKey);
      if (token) {
        method.config.headers[tokenHeader] = `${tokenType} ${token}`;
      }
    } catch (error) {
      console.error("获取Token失败:", error);
    }
  },

  // 登出处理
  logout() {
    mitter.emit("ROUTER_LOGIN_EXPIRED", () => {});
  },
});

/**
 * 获取当前token
 * @returns {Promise<string|null>} 当前token或null
 */
export async function getCurrentToken() {
  try {
    return await storage.get(accessTokenKey);
  } catch (error) {
    console.error("获取当前Token失败:", error);
    return null;
  }
}

/**
 * 清除所有认证信息
 * @returns {Promise<void>}
 */
export async function clearAuthInfo() {
  try {
    await storage.remove(accessTokenKey);
    await storage.remove(refreshTokenKey);
  } catch (error) {
    console.error("清除认证信息失败:", error);
  }
}
