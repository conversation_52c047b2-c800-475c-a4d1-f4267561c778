/*
 * 动态引入css文件
 * @param {string} - url 需引入的css地址
 * @return 无
 * */
export function loadExternalCSS(url) {
  const link = document.createElement('link')
  link.href = url
  link.rel = 'stylesheet'
  document.head.appendChild(link)
}

/*
 * 表单序列化
 * @param {Object} - data 需要格式化的数据
 * @return url参数
 * */
export function serialize(data) {
  if (data) {
    const params = new URLSearchParams(data)
    return params.toString()
  }
  return ''
}
