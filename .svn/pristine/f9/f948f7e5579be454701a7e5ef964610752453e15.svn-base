/**
 * ================================================================================================
 * @cm/alova 企业级类型声明文件 - 完全重构版本
 * ================================================================================================
 * 
 * 基于 alova 类型系统构建的企业级 HTTP 客户端类型定义
 * 提供类型安全、功能完整的企业开发解决方案
 * 
 * 🚀 核心特性：
 * ├── 完整的 alova 生态集成 (100% 基于 alova/alova-client 类型)
 * ├── 企业级错误处理和异常管理
 * ├── 强类型的认证和权限系统  
 * ├── 智能Loading管理和性能监控
 * ├── 全面的 HTTP 方法支持 (GET/POST/PUT/DELETE/PATCH/HEAD/OPTIONS)
 * ├── 丰富的工具类型和开发者友好的别名
 * └── 模块化架构和清晰的类型组织
 * 
 * 📋 架构设计：
 * ├── 核心类型导入 (从 alova 和 alova/client)
 * ├── 企业级泛型类型系统
 * ├── 配置和选项管理
 * ├── 错误处理和异常系统
 * ├── 认证和权限管理
 * ├── 性能监控和Loading管理
 * ├── HTTP 方法基类和客户端
 * ├── 工具类型和便捷别名
 * └── 实例导出和构造函数
 * 
 * <AUTHOR>
 * @version 4.0.0
 * @since 2024
 */

// ================================================================================================
// 🎯 核心类型导入 - 从 alova 和 alova/client 导入所有必需类型
// ================================================================================================

// 从 alova 核心包导入完整类型集合
import {
  // 🔧 核心实例和方法类型
  Alova,
  Method,
  AlovaGenerics,
  RequestBody,
  MethodType,
  RequestElements,
  
  // 📝 配置和选项类型
  AlovaMethodConfig,
  AlovaMethodCreateConfig,
  AlovaMethodCommonConfig,
  AlovaOptions,
  MethodRequestConfig,
  AlovaRequestAdapter,
  
  // 📡 响应处理类型
  RespondedHandler,
  ResponseErrorHandler,
  ResponseCompleteHandler,
  RespondedHandlerRecord,
  RespondedAlovaGenerics,
  
  // 🗄️ 缓存系统类型
  CacheExpire,
  CacheMode,
  DetailCacheConfig,
  CacheConfig,
  GlobalCacheConfig,
  CacheController,
  CacheLoggerHandler,
  AlovaGlobalCacheAdapter,
  AlovaDefaultCacheAdapter,
  
  // 📊 进度和控制类型
  Progress,
  ProgressHandler,
  AbortFunction,
  
  // 🔍 快照和过滤类型
  MethodSnapshotContainer,
  MethodFilter,
  
  // 🏗️ 状态管理类型
  StatesHook,
  StatesExport,
  FrontRequestState,
  FetchRequestState,
  
  // 🌍 全局配置类型
  AlovaGlobalConfig,
  
  // 🔌 适配器类型
  AlovaRequestAdapter as BaseAlovaRequestAdapter
} from 'alova';

// 从 alova/client 导入客户端钩子类型
import type * as AlovaClientTypes from 'alova/client';

// 重新导出 alova/client 的所有类型供外部使用
export * from 'alova/client';

// ================================================================================================
// 🎨 企业级泛型类型系统 - 精确的类型约束和企业级扩展
// ================================================================================================

/**
 * 🎯 企业级默认泛型类型 - 为企业应用优化的类型约束
 * 
 * 使用更精确的类型定义确保运行时类型安全
 * 支持现代浏览器和Node.js环境的标准类型
 * 
 * @template TResponse 响应数据类型 - 服务端返回的原始数据
 * @template TTransformed 转换后的数据类型 - 经过transform处理的数据
 * @template TRequestConfig 请求配置类型 - 扩展的请求配置
 * @template TResponse HTTP响应对象类型 - 标准Response接口
 * @template TResponseHeader HTTP响应头类型 - 标准Headers接口
 */
export type DefaultAlovaGenerics = AlovaGenerics<
  any, // Responded - 响应数据类型
  any, // Transformed - 转换后的数据类型  
  Record<string, any>, // RequestConfig - 请求配置类型
  Response, // Response - 标准HTTP Response
  Headers, // ResponseHeader - 标准HTTP Headers
  AlovaDefaultCacheAdapter, // L1Cache - 一级缓存适配器
  AlovaDefaultCacheAdapter, // L2Cache - 二级缓存适配器
  StatesExport // StatesExport - 状态导出类型
>;

/**
 * 🏢 企业级专用泛型类型 - 为特定业务场景定制
 * 
 * 提供更严格的类型约束和企业级功能支持
 * 适用于需要高度类型安全的企业应用
 */
export type EnterpriseAlovaGenerics<
  TResponse = any,
  TTransformed = TResponse,
  TRequestConfig extends Record<string, any> = Record<string, any>
> = AlovaGenerics<
  TResponse,
  TTransformed,
  TRequestConfig,
  Response,
  Headers,
  AlovaDefaultCacheAdapter,
  AlovaDefaultCacheAdapter,
  StatesExport
>;

/**
 * 🎭 HTTP方法泛型约束 - 确保类型安全的方法调用
 * 
 * 为HTTP方法提供精确的泛型约束
 * 支持响应数据类型推导和转换
 */
export type HttpMethodGenerics<TResponse = any, TTransformed = TResponse> = 
  RespondedAlovaGenerics<DefaultAlovaGenerics, TResponse, TTransformed>;

// ================================================================================================
// ⚙️ 企业级配置系统 - 基于 alova 的扩展配置体系
// ================================================================================================

/**
 * 🔧 企业级HTTP请求配置 - 扩展alova配置的企业功能
 * 
 * 基于 alova AlovaMethodCreateConfig 构建
 * 添加企业级特性：数据转换、缓存控制、Loading管理等
 */
export interface EnterpriseHttpRequestConfig<
  AG extends AlovaGenerics = DefaultAlovaGenerics,
  TResponse = any,
  TTransformed = TResponse
> extends AlovaMethodCreateConfig<AG, TResponse, TTransformed> {
  /** 🔄 数据转换函数 - 响应数据预处理 */
  transformData?: (data: any, headers: AG['ResponseHeader']) => any;
  
  /** ⚡ 本地缓存配置 - 使用alova缓存类型 */
  localCache?: CacheExpire;
  
  /** 🚫 跳过Loading显示 - 静默请求 */
  skipLoading?: boolean;
  
  /** 🔄 自动重试配置 */
  retry?: {
    /** 重试次数 */
    times: number;
    /** 重试延迟(毫秒) */
    delay: number;
    /** 重试条件判断 */
    when?: (error: any) => boolean;
  };
  
  /** 📝 请求标签 - 用于分组和管理 */
  tags?: string[];
  
  /** 🎯 业务标识 - 用于统计和监控 */
  bizKey?: string;
  
  /** 🔍 调试模式 - 开发环境调试信息 */
  debug?: boolean;
  
  /** 📊 性能监控 - 性能数据收集 */
  performance?: {
    /** 是否启用监控 */
    enabled: boolean;
    /** 监控数据回调 */
    onMetrics?: (metrics: PerformanceMetrics) => void;
  };
  
  /** 🔧 扩展字段 - 支持自定义配置 */
  [key: string]: any;
}

/**
 * 📊 性能监控指标类型
 */
export interface PerformanceMetrics {
  /** 请求开始时间戳 */
  startTime: number;
  /** 请求结束时间戳 */
  endTime: number;
  /** 请求持续时间(毫秒) */
  duration: number;
  /** 请求URL */
  url: string;
  /** 请求方法 */
  method: MethodType;
  /** 响应状态码 */
  status?: number;
  /** 响应体大小(字节) */
  responseSize?: number;
  /** 是否来自缓存 */
  fromCache: boolean;
}

/**
 * 🏢 企业级Alova选项配置 - 完整的企业功能配置
 */
export interface EnterpriseAlovaOptions<AG extends AlovaGenerics = DefaultAlovaGenerics> 
  extends AlovaOptions<AG> {
  /** 🏢 企业级错误处理器 */
  enterpriseErrorHandler?: EnterpriseErrorHandler<AG>;
  
  /** 🔐 企业级认证配置 */
  authConfig?: EnterpriseAuthConfig<AG>;
  
  /** 📱 Loading管理配置 */
  loadingConfig?: LoadingManagerConfig;
  
  /** 📊 性能监控配置 */
  performanceConfig?: PerformanceConfig;
  
  /** 🔍 调试和日志配置 */
  debugConfig?: DebugConfig;
}

// ================================================================================================
// 🚨 企业级错误处理系统 - 基于 alova ResponseErrorHandler 的完整错误管理
// ================================================================================================

/**
 * 🚨 企业级错误上下文 - 完整的错误信息收集
 * 
 * 基于 alova 错误处理机制，提供企业级错误追踪
 * 支持错误分类、链路追踪、性能分析等功能
 */
export interface EnterpriseErrorContext {
  /** 🏷️ 错误类型标识 */
  type: 'network' | 'timeout' | 'abort' | 'business' | 'validation' | 'auth' | 'system';
  
  /** 📝 错误消息 */
  message: string;
  
  /** 🌐 请求URL */
  url: string;
  
  /** 🔧 HTTP方法 - 使用alova MethodType */
  method: MethodType;
  
  /** ⏰ 错误发生时间戳 */
  timestamp: number;
  
  /** 📊 HTTP状态码 */
  status: number | null;
  
  /** 📋 错误堆栈信息 */
  stack: string | null;
  
  /** 🔍 请求ID - 用于链路追踪 */
  requestId: string | null;
  
  /** ⏱️ 请求持续时间(毫秒) */
  duration: number | null;
  
  /** 🎯 关联的alova Method实例 */
  methodInstance: Method<any>;
  
  /** 📱 用户代理信息 */
  userAgent?: string;
  
  /** 🌍 用户IP地址 */
  clientIp?: string;
  
  /** 🔄 重试次数 */
  retryCount?: number;
  
  /** 🏷️ 业务标签 */
  bizKey?: string;
  
  /** 📊 性能指标 */
  performanceMetrics?: PerformanceMetrics;
}

/**
 * 🎯 企业级错误处理结果 - 错误处理后的标准化结果
 */
export interface EnterpriseErrorHandlingResult {
  /** 📝 处理后的错误消息 */
  message: string;
  
  /** ⚠️ 错误严重级别 */
  severity: 'low' | 'medium' | 'high' | 'critical';
  
  /** 🎭 错误展示方式 */
  displayType: 'silent' | 'toast' | 'modal' | 'page';
  
  /** 🔄 是否应该重试 */
  shouldRetry: boolean;
  
  /** ⏰ 重试延迟时间(毫秒) */
  retryDelay?: number;
  
  /** 📊 错误元数据 */
  metadata?: {
    /** 🔧 处理器类型 */
    handlerType: string;
    /** 📈 错误统计标识 */
    statsKey?: string;
    /** 🔍 错误分组标识 */
    groupKey?: string;
    /** 📝 用户友好的错误提示 */
    userMessage?: string;
    /** 🔗 帮助文档链接 */
    helpUrl?: string;
    /** 🎯 错误代码 */
    errorCode?: string | number;
    /** 🔧 扩展字段 */
    [key: string]: any;
  };
}

/**
 * 🏢 企业级错误处理器 - 基于alova ResponseErrorHandler的企业级扩展
 */
export type EnterpriseErrorHandler<AG extends AlovaGenerics = DefaultAlovaGenerics> = (
  error: any,
  method: Method<AG>
) => EnterpriseErrorHandlingResult | Promise<EnterpriseErrorHandlingResult>;

// ================================================================================================
// 🔐 企业级认证系统 - 基于 alova RespondedHandlerRecord 的完整认证解决方案  
// ================================================================================================

/**
 * 🔐 企业级认证配置 - 完全基于alova响应处理钩子系统
 * 
 * 使用 alova 的 RespondedHandlerRecord 和相关类型构建
 * 提供完整的认证生命周期管理
 */
export interface EnterpriseAuthConfig<AG extends AlovaGenerics = DefaultAlovaGenerics> {
  /** 🛡️ 认证拦截器 - 基于alova beforeRequest钩子 */
  onAuthRequired: (
    handler: (method: Method<AG>) => void | Promise<void>
  ) => (method: Method<AG>) => void | Promise<void>;
  
  /** 🔄 Token刷新处理器 - 基于alova RespondedHandlerRecord */
  onTokenRefresh: (config: RespondedHandlerRecord<AG>) => RespondedHandlerRecord<AG>;
  
  /** ❌ 认证失败处理 - 使用alova ResponseErrorHandler */
  onAuthError?: ResponseErrorHandler<AG>;
  
  /** ✅ 登录成功处理 - 使用alova RespondedHandler */
  onAuthSuccess?: RespondedHandler<AG>;
  
  /** 🚪 登出处理 - 使用alova ResponseCompleteHandler */
  onLogout?: ResponseCompleteHandler<AG>;
  
  /** ⏰ Token有效性检查 */
  tokenValidator?: {
    /** Token有效期检查(秒) */
    checkInterval: number;
    /** Token过期前预刷新时间(秒) */
    refreshBeforeExpire: number;
    /** Token验证函数 */
    validate: (token: string) => Promise<boolean>;
  };
  
  /** 🔑 认证存储配置 */
  storage?: {
    /** Token存储键名 */
    tokenKey: string;
    /** 用户信息存储键名 */
    userInfoKey: string;
    /** 存储类型 */
    type: 'localStorage' | 'sessionStorage' | 'memory' | 'custom';
    /** 自定义存储实现 */
    customStorage?: {
      getItem: (key: string) => string | null;
      setItem: (key: string, value: string) => void;
      removeItem: (key: string) => void;
    };
  };
  
  /** 🌐 认证端点配置 */
  endpoints?: {
    /** 登录端点 */
    login: string;
    /** 登出端点 */
    logout: string;
    /** Token刷新端点 */
    refresh: string;
    /** 用户信息获取端点 */
    userInfo: string;
  };
}

/**
 * 🎭 认证状态类型
 */
export interface AuthState {
  /** 🔐 是否已认证 */
  isAuthenticated: boolean;
  /** 👤 用户信息 */
  user: any;
  /** 🎫 访问令牌 */
  accessToken: string | null;
  /** 🔄 刷新令牌 */
  refreshToken: string | null;
  /** ⏰ Token过期时间 */
  expiresAt: number | null;
  /** 📊 认证状态 */
  status: 'idle' | 'loading' | 'authenticated' | 'unauthenticated' | 'error';
}

// ================================================================================================
// 📱 智能Loading管理系统 - 与 alova 深度集成的性能监控
// ================================================================================================

/**
 * 📱 Loading状态定义
 */
export interface LoadingStatus {
  /** 🎭 当前状态 */
  state: 'idle' | 'loading' | 'waiting_min_time' | 'hiding';
  /** 🔄 是否正在加载 */
  isLoading: boolean;
  /** 📊 活跃请求数量 */
  activeRequestCount: number;
  /** ⏰ 加载开始时间戳 */
  loadingStartTime: number;
  /** ⚙️ Loading配置 */
  config: LoadingManagerConfig;
  /** 🚨 错误提示队列 */
  errorToastQueue: {
    /** 队列大小 */
    queueSize: number;
    /** 是否正在处理 */
    isProcessing: boolean;
    /** 消息列表 */
    messages: string[];
  };
}

/**
 * ⚙️ Loading管理器配置
 */
export interface LoadingManagerConfig {
  /** 🔄 是否启用Loading */
  enabled: boolean;
  /** 🚫 跳过Loading的请求头标识 */
  skipLoadingHeader: string;
  /** ⏱️ 最小Loading显示时间(毫秒) */
  minLoadingTime: number;
  /** 📝 Loading文案 */
  message: string;
  /** ⏰ 错误提示防抖时间(毫秒) */
  errorToastDebounce: number;
  /** 🎨 Loading样式配置 */
  toastConfig: {
    /** 🚫 禁止点击 */
    forbidClick: boolean;
    /** 🎭 Loading类型 */
    loadingType: string;
    /** ⏰ 显示持续时间(毫秒) */
    duration: number;
    /** 🎭 是否显示遮罩 */
    overlay: boolean;
    /** 🎨 遮罩样式 */
    overlayStyle: Record<string, any>;
  };
}

/**
 * 📊 性能监控配置
 */
export interface PerformanceConfig {
  /** 🔄 是否启用性能监控 */
  enabled: boolean;
  /** 📊 性能数据收集间隔(毫秒) */
  sampleInterval: number;
  /** 🎯 慢请求阈值(毫秒) */
  slowRequestThreshold: number;
  /** 📈 性能数据回调 */
  onPerformanceData?: (data: PerformanceMetrics[]) => void;
}

/**
 * 🔍 调试配置
 */
export interface DebugConfig {
  /** 🔄 是否启用调试 */
  enabled: boolean;
  /** 📝 日志级别 */
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  /** 📊 是否显示性能信息 */
  showPerformance: boolean;
  /** 🌐 是否显示请求详情 */
  showRequestDetails: boolean;
}

/**
 * 🏢 企业级Loading管理器 - 与alova Method和Progress深度集成
 * 
 * 基于 alova 的 Method、Progress 和 ProgressHandler 类型构建
 * 提供企业级的Loading状态管理和性能监控
 */
export interface EnterpriseLoadingManager<AG extends AlovaGenerics = DefaultAlovaGenerics> {
  /** 🚀 开始请求跟踪 - 接受alova Method实例 */
  startRequest(method: Method<AG>): string | null;
  
  /** 🏁 结束请求跟踪 */
  endRequest(requestId: string | null): void;
  
  /** 🚨 添加错误提示 */
  addErrorToast(message: string): boolean;
  
  /** 📊 获取活跃请求数量 */
  getActiveRequestCount(): number;
  
  /** 📋 获取活跃请求列表 - 返回完整的alova Method信息 */
  getActiveRequests(): Array<{
    /** 🆔 请求唯一标识 */
    id: string;
    /** 🌐 请求URL */
    url: string;
    /** 🔧 HTTP方法 - 使用alova MethodType */
    type: MethodType;
    /** ⏰ 请求开始时间戳 */
    startTime: number;
    /** 🎯 alova Method实例 */
    method: Method<AG>;
    /** 📊 进度信息 - 使用alova Progress类型 */
    progress?: Progress;
    /** 📈 性能指标 */
    performanceMetrics?: PerformanceMetrics;
  }>;
  
  /** 🔄 重置管理器状态 */
  reset(): void;
  
  /** ⚙️ 配置管理器 */
  configure(config: Partial<LoadingManagerConfig>): void;
  
  /** 📊 获取错误提示状态 */
  getErrorToastStatus(): {
    queueSize: number;
    isProcessing: boolean;
    messages: string[];
  };
  
  /** 📱 获取Loading状态 */
  getStatus(): LoadingStatus;
  
  /** 📈 绑定进度处理器 - 使用alova ProgressHandler */
  onProgress?: ProgressHandler;
  
  /** 🎯 性能监控回调 */
  onPerformanceMetrics?: (metrics: PerformanceMetrics) => void;
  
  /** 🚨 错误处理回调 */
  onError?: (error: EnterpriseErrorContext) => void;
}

// ================================================================================================
// 🌐 HTTP方法基类 - 基于alova的完整HTTP方法支持
// ================================================================================================

/**
 * 🌐 企业级HTTP方法基类 - 完全基于alova类型系统构建
 * 
 * 提供企业级的类型安全HTTP请求方法封装
 * 支持完整的HTTP方法集合和高级企业功能
 * 
 * 🚀 特性：
 * ├── 完整的HTTP方法支持 (GET/POST/PUT/DELETE/PATCH/HEAD/OPTIONS)
 * ├── 强类型的参数和返回值约束  
 * ├── 基于alova的配置系统
 * ├── 企业级错误处理集成
 * ├── 性能监控和调试支持
 * └── 灵活的泛型约束系统
 */
export interface EnterpriseMethods<AG extends AlovaGenerics = DefaultAlovaGenerics> {
  /** 
   * 🎯 alova实例引用 - 只读属性确保类型安全
   * @readonly 防止外部修改确保实例稳定性
   */
  readonly alovaInstance: Alova<AG>;
  
  /**
   * 🔧 通用请求方法 - 创建alova Method请求实例  
   * @param config 使用alova的AlovaMethodCommonConfig类型
   * @returns alova的Method泛型实例
   */
  request<TResponse = any, TTransformed = TResponse>(
    config: AlovaMethodCommonConfig<AG, TResponse, TTransformed>
  ): Method<RespondedAlovaGenerics<AG, TResponse, TTransformed>>;
  
  /**
   * 📥 GET请求方法 - 数据获取
   * @param url 请求URL
   * @param config 使用alova的AlovaMethodCreateConfig类型
   * @returns alova的Method泛型实例
   */
  get<TResponse = any, TTransformed = TResponse>(
    url: string, 
    config?: EnterpriseHttpRequestConfig<AG, TResponse, TTransformed>
  ): Method<RespondedAlovaGenerics<AG, TResponse, TTransformed>>;
  
  /**
   * 📤 POST请求方法 - 数据创建
   * @param url 请求URL  
   * @param data 基于alova RequestBody类型的请求数据
   * @param config 使用alova的AlovaMethodCreateConfig类型
   * @returns alova的Method泛型实例
   */
  post<TResponse = any, TTransformed = TResponse>(
    url: string, 
    data?: RequestBody, 
    config?: EnterpriseHttpRequestConfig<AG, TResponse, TTransformed>
  ): Method<RespondedAlovaGenerics<AG, TResponse, TTransformed>>;
  
  /**
   * ✏️ PUT请求方法 - 数据更新(完整替换)
   * @param url 请求URL  
   * @param data 基于alova RequestBody类型的请求数据
   * @param config 使用alova的AlovaMethodCreateConfig类型
   * @returns alova的Method泛型实例
   */
  put<TResponse = any, TTransformed = TResponse>(
    url: string, 
    data?: RequestBody, 
    config?: EnterpriseHttpRequestConfig<AG, TResponse, TTransformed>
  ): Method<RespondedAlovaGenerics<AG, TResponse, TTransformed>>;
  
  /**
   * 🗑️ DELETE请求方法 - 数据删除
   * @param url 请求URL  
   * @param data 基于alova RequestBody类型的请求数据(可选)
   * @param config 使用alova的AlovaMethodCreateConfig类型
   * @returns alova的Method泛型实例
   */
  delete<TResponse = any, TTransformed = TResponse>(
    url: string, 
    data?: RequestBody, 
    config?: EnterpriseHttpRequestConfig<AG, TResponse, TTransformed>
  ): Method<RespondedAlovaGenerics<AG, TResponse, TTransformed>>;
  
  /**
   * 🔧 PATCH请求方法 - 数据更新(部分修改)
   * @param url 请求URL  
   * @param data 基于alova RequestBody类型的请求数据
   * @param config 使用alova的AlovaMethodCreateConfig类型
   * @returns alova的Method泛型实例
   */
  patch<TResponse = any, TTransformed = TResponse>(
    url: string, 
    data?: RequestBody, 
    config?: EnterpriseHttpRequestConfig<AG, TResponse, TTransformed>
  ): Method<RespondedAlovaGenerics<AG, TResponse, TTransformed>>;
  
  /**
   * 📋 HEAD请求方法 - 获取资源头信息
   * @param url 请求URL
   * @param config 使用alova的AlovaMethodCreateConfig类型
   * @returns alova的Method泛型实例
   */
  head<TResponse = any, TTransformed = TResponse>(
    url: string, 
    config?: EnterpriseHttpRequestConfig<AG, TResponse, TTransformed>
  ): Method<RespondedAlovaGenerics<AG, TResponse, TTransformed>>;
  
  /**
   * ⚙️ OPTIONS请求方法 - 获取资源支持的方法
   * @param url 请求URL
   * @param config 使用alova的AlovaMethodCreateConfig类型
   * @returns alova的Method泛型实例
   */
  options<TResponse = any, TTransformed = TResponse>(
    url: string, 
    config?: EnterpriseHttpRequestConfig<AG, TResponse, TTransformed>
  ): Method<RespondedAlovaGenerics<AG, TResponse, TTransformed>>;
}

// ================================================================================================
// 🏢 企业级HTTP客户端 - 完整功能集成的终极解决方案
// ================================================================================================

/**
 * 🎯 自动提取alova/client中的所有函数类型
 * 
 * 通过TypeScript条件类型自动识别并提取alova/client的所有可调用方法
 * 确保企业级客户端能够完整继承alova/client的所有钩子功能
 */
type AlovaClientMethods = {
  [K in keyof typeof AlovaClientTypes]: typeof AlovaClientTypes[K] extends (...args: any[]) => any 
    ? typeof AlovaClientTypes[K] 
    : never;
};

/**
 * 🏢 企业级HTTP客户端接口 - 功能完整的企业解决方案
 * 
 * 🚀 继承特性：
 * ├── EnterpriseMethods 的所有基础HTTP方法 (GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS)
 * ├── AlovaClientMethods 的所有客户端钩子 (useRequest, useFetcher, useWatcher等)
 * ├── 企业级Loading管理和性能监控
 * ├── 完整的错误处理和认证系统  
 * └── 类型安全的配置和扩展能力
 * 
 * 🎯 应用场景：
 * ├── 企业级Web应用的HTTP客户端
 * ├── 微前端架构的统一请求层
 * ├── 需要高度类型安全的TypeScript项目
 * └── 复杂业务场景的数据管理
 */
export interface EnterpriseHttpClient<AG extends AlovaGenerics = DefaultAlovaGenerics> 
  extends EnterpriseMethods<AG>, AlovaClientMethods {
  
  /**
   * 📱 Loading管理器实例 - 与alova深度集成
   * 提供智能的请求状态管理和用户体验优化
   */
  loadingManager?: EnterpriseLoadingManager<AG>;
  
  /**
   * 🎯 alova实例引用 - 只读属性确保类型安全
   * @readonly 防止外部修改保证实例稳定性
   */
  readonly alovaInstance: Alova<AG>;
  
  /**
   * 🚨 错误处理器 - 基于alova ResponseErrorHandler
   * 提供企业级的错误分类、处理和恢复机制
   */
  errorHandler?: EnterpriseErrorHandler<AG>;
  
  /**
   * 🔐 认证配置 - 基于alova响应处理钩子
   * 提供完整的认证生命周期管理
   */
  authConfig?: EnterpriseAuthConfig<AG>;
  
  /**
   * 📊 性能监控器 - 实时性能数据收集和分析
   * 支持慢请求检测、性能统计和优化建议
   */
  performanceMonitor?: {
    /** 📈 获取性能指标 */
    getMetrics(): PerformanceMetrics[];
    /** 📊 获取统计数据 */
    getStats(): {
      totalRequests: number;
      averageResponseTime: number;
      slowRequestCount: number;
      errorRate: number;
    };
    /** 🔄 重置统计数据 */
    reset(): void;
  };
  
  /**
   * 🔍 调试工具 - 开发环境调试支持
   */
  debugTools?: {
    /** 📝 启用请求日志 */
    enableRequestLogging(enabled: boolean): void;
    /** 📊 导出调试数据 */
    exportDebugData(): any;
    /** 🔍 请求拦截器 */
    interceptRequest(interceptor: (method: Method<AG>) => void): void;
  };
}

// ================================================================================================
// 🛠️ 工具类型和便捷别名 - 开发者友好的类型工具集
// ================================================================================================

/**
 * 🎯 企业级Method类型别名 - 使用精确的泛型约束
 */
export type EnterpriseMethod<
  TResponse = any, 
  AG extends AlovaGenerics = DefaultAlovaGenerics
> = Method<RespondedAlovaGenerics<AG, TResponse, TResponse>>;

/**
 * 📡 HTTP响应类型别名 - 保持简洁的响应类型定义
 */
export type HttpResponse<T = any> = T;

/**
 * 🚨 企业级HTTP错误类型 - 基于标准Error的完整错误信息
 * 
 * 与alova的错误处理系统完全兼容
 * 提供企业级的错误追踪和诊断能力
 */
export interface EnterpriseHttpError extends Error {
  /** 📊 HTTP状态码 */
  status?: number;
  /** 🎯 业务错误码 */
  code?: number | string;
  /** 📄 原始响应数据 */
  response?: any;
  /** 🔗 关联的alova Method实例 */
  method?: Method<any>;
  /** ⏰ 错误发生时间戳 */
  timestamp?: number;
  /** 🔍 请求ID用于链路追踪 */
  requestId?: string;
  /** 📊 性能指标 */
  performanceMetrics?: PerformanceMetrics;
  /** 🏷️ 错误类型标识 */
  errorType?: string;
  /** 🔄 重试次数 */
  retryCount?: number;
}

/**
 * 📊 基于alova FrontRequestState的请求状态类型
 */
export type EnterpriseRequestState<TResponse = any> = 
  FrontRequestState<boolean, TResponse, EnterpriseHttpError, Progress, Progress>;

/**
 * 🔍 基于alova FetchRequestState的获取状态类型
 */
export type EnterpriseFetchState = 
  FetchRequestState<boolean, EnterpriseHttpError, Progress, Progress>;

/**
 * ⚙️ 基于alova的请求配置类型别名
 */
export type EnterpriseRequestConfig<
  TResponse = any,
  TTransformed = TResponse,
  AG extends AlovaGenerics = DefaultAlovaGenerics
> = AlovaMethodCreateConfig<AG, TResponse, TTransformed>;

/**
 * 🔧 基于alova的通用配置类型别名
 */
export type EnterpriseCommonConfig<
  TResponse = any,
  TTransformed = TResponse,
  AG extends AlovaGenerics = DefaultAlovaGenerics
> = AlovaMethodCommonConfig<AG, TResponse, TTransformed>;

/**
 * 🗄️ 基于alova缓存控制器的企业级缓存类型
 */
export type EnterpriseCacheController<TResponse = any> = CacheController<TResponse>;

/**
 * 🔌 基于alova缓存适配器的企业级缓存适配器
 */
export type EnterpriseCacheAdapter = AlovaGlobalCacheAdapter;

/**
 * 🔍 企业级方法过滤器 - 基于alova MethodFilter
 */
export type EnterpriseMethodFilter<AG extends AlovaGenerics = DefaultAlovaGenerics> = 
  MethodFilter<AG>;

/**
 * 📸 企业级快照容器 - 基于alova MethodSnapshotContainer
 */
export type EnterpriseSnapshotContainer<AG extends AlovaGenerics = DefaultAlovaGenerics> = 
  MethodSnapshotContainer<AG>;

/**
 * 🎭 企业级状态钩子 - 基于alova StatesHook
 */
export type EnterpriseStatesHook<SE extends StatesExport<any> = StatesExport> = 
  StatesHook<SE>;

// ================================================================================================
// 🔄 alova核心类型重新导出 - 完整的类型生态系统
// ================================================================================================

// 导出所有alova核心类型，确保开发者可以直接使用完整的alova类型系统
export type { 
  // 🔧 核心实例和方法类型
  Alova,
  Method, 
  MethodType, 
  RequestBody,
  RequestElements,
  
  // 📝 泛型和配置类型
  AlovaGenerics,
  AlovaMethodConfig,
  AlovaMethodCreateConfig,
  AlovaMethodCommonConfig,
  AlovaOptions,
  MethodRequestConfig,
  
  // 📊 进度和控制类型
  Progress,
  ProgressHandler,
  AbortFunction,
  
  // 🔍 快照和过滤类型
  MethodSnapshotContainer,
  MethodFilter,
  
  // 📡 响应处理类型
  RespondedHandler,
  ResponseErrorHandler,
  ResponseCompleteHandler,
  RespondedHandlerRecord,
  RespondedAlovaGenerics,
  
  // 🗄️ 缓存系统类型
  CacheExpire,
  CacheMode,
  DetailCacheConfig,
  CacheConfig,
  GlobalCacheConfig,
  CacheController,
  CacheLoggerHandler,
  AlovaGlobalCacheAdapter,
  AlovaDefaultCacheAdapter,
  
  // 🏗️ 状态管理类型
  StatesHook,
  StatesExport,
  FrontRequestState,
  FetchRequestState,
  
  // 🌍 全局配置类型
  AlovaGlobalConfig,
  
  // 🔌 适配器类型
  BaseAlovaRequestAdapter as AlovaRequestAdapter
} from 'alova';

// ================================================================================================
// 🏗️ 构造函数和实例类型定义
// ================================================================================================

/**
 * 🏗️ 企业级HTTP客户端构造函数类型 - 支持泛型参数
 */
export interface EnterpriseHttpClientConstructor<AG extends AlovaGenerics = DefaultAlovaGenerics> {
  new (): EnterpriseHttpClient<AG>;
  prototype: EnterpriseHttpClient<AG>;
}

/**
 * 🔧 企业级Methods基类构造函数类型 - 支持泛型参数
 */
export interface EnterpriseMethodsConstructor<AG extends AlovaGenerics = DefaultAlovaGenerics> {
  new (): EnterpriseMethods<AG>;
  prototype: EnterpriseMethods<AG>;
}

// ================================================================================================
// 📤 实例导出和API声明
// ================================================================================================

/**
 * 🎯 注意事项：
 * 
 * 所有 alova/client 的类型 (useRequest, useFetcher, useWatcher 等) 
 * 都已通过 `export * from 'alova/client'` 自动导出
 * 开发者可直接使用，无需额外导入
 * 
 * 🚀 可用的alova/client钩子：
 * ├── useRequest - 基础请求钩子
 * ├── useFetcher - 数据获取钩子  
 * ├── useWatcher - 监听器钩子
 * ├── usePagination - 分页钩子
 * ├── useForm - 表单钩子
 * └── 其他所有alova/client导出的类型和函数
 */

/**
 * 🏢 默认的企业级HTTP客户端实例
 * 
 * 集成了alova的所有功能和企业级扩展特性
 * 提供完整的类型安全和开发体验优化
 * 
 * 🚀 使用示例：
 * ```typescript
 * import http from '@cm/alova';
 * 
 * // 基础HTTP请求
 * const userMethod = http.get<User>('/api/user/profile');
 * 
 * // 使用alova/client钩子
 * const { data, loading, error } = http.useRequest(userMethod);
 * 
 * // 企业级功能
 * http.loadingManager?.configure({ enabled: true });
 * ```
 */
declare const http: EnterpriseHttpClient<DefaultAlovaGenerics>;
export default http;

/**
 * 🔧 Methods基类导出 - 支持自定义继承和扩展
 * 
 * 🚀 使用示例：
 * ```typescript
 * import { Methods } from '@cm/alova';
 * 
 * class CustomHttpClient extends Methods {
 *   // 自定义扩展实现
 * }
 * ```
 */
export declare const Methods: EnterpriseMethodsConstructor<DefaultAlovaGenerics>;

/**
 * 📱 Loading管理器实例导出 - 企业级Loading状态管理
 * 
 * 🚀 使用示例：
 * ```typescript
 * import { loadingManager } from '@cm/alova';
 * 
 * loadingManager.configure({ minLoadingTime: 500 });
 * const activeRequests = loadingManager.getActiveRequests();
 * ```
 */
export declare const loadingManager: EnterpriseLoadingManager<DefaultAlovaGenerics>;

/**
 * 🔐 认证相关功能导出
 * 
 * 🚀 使用示例：
 * ```typescript
 * import { getCurrentToken, clearAuthInfo } from '@cm/alova';
 * 
 * const token = await getCurrentToken();
 * await clearAuthInfo();
 * ```
 */
export declare function getCurrentToken(): Promise<string | null>;
export declare function clearAuthInfo(): Promise<void>;

/**
 * 🎯 创建企业级HTTP客户端实例的工厂函数
 * 
 * 🚀 使用示例：
 * ```typescript
 * import { createEnterpriseHttpClient } from '@cm/alova';
 * 
 * const customClient = createEnterpriseHttpClient({
 *   baseURL: 'https://api.example.com',
 *   authConfig: { ... },
 *   loadingConfig: { ... }
 * });
 * ```
 */
export declare function createEnterpriseHttpClient<AG extends AlovaGenerics = DefaultAlovaGenerics>(
  options: EnterpriseAlovaOptions<AG>
): EnterpriseHttpClient<AG>;

// ================================================================================================
// 🎯 类型便捷导出 - 开发者友好的类型别名集合
// ================================================================================================

// 为了方便使用，提供一些常用类型的简化别名
export type HttpClient = EnterpriseHttpClient;
export type HttpMethod = EnterpriseMethod;
export type HttpError = EnterpriseHttpError;
export type HttpConfig = EnterpriseHttpRequestConfig;
export type HttpState<T = any> = EnterpriseRequestState<T>;
export type FetchState = EnterpriseFetchState;

/**
 * 🎉 @cm/alova 企业级类型系统构建完成！
 * 
 * ✅ 100% 基于 alova 和 alova/client 类型构建
 * ✅ 完整的企业级功能支持
 * ✅ 模块化的架构设计
 * ✅ 丰富的工具类型和别名
 * ✅ 完善的文档和使用示例
 * ✅ 类型安全和开发体验优化
 * 
 * 🚀 立即开始使用企业级HTTP客户端！
 */