import http from "@cm/alova";

/**
 * 预警列表-学生
 * @param {*} params
 * @returns
 */
export function getAlarmAppStudentListAPI(params) {
  return http.get("/blade-yjxxcj/yjxxCj/appStudentPage", { params });
}
/**
 * 预警列表--校领导-院系专业
 * @param {*} params
 * @returns
 */
export function getAlarmListAPI(params) {
  return http.get("/blade-yjxxcj/yjxxCj/majorPage", { params });
}

/**
 * 预警列表--校领导-班级
 * @param {*} params
 * @returns
 */
export function getAlarmClassListAPI(params) {
  return http.get("/blade-yjxxcj/yjxxCj/classPage", { params });
}

/**
 * 预警列表--校领导-学生
 * @param {*} params
 * @returns
 */
export function getAlarmStudentListAPI(params) {
  return http.get("/blade-yjxxcj/yjxxCj/studentPage", { params });
}
/**
 * 预警列表--辅导员-班级
 * @param {*} params
 * @returns
 */
export function getAlarmInstructorClassListAPI(params) {
  return http.get("/blade-yjxxcj/yjxxCj/instructorClassPage", { params });
}
/**
 * 预警列表--辅导员-学生
 * @param {*} params
 * @returns
 */
export function getAlarmInstructorStudentListAPI(params) {
  return http.get("/blade-yjxxcj/yjxxCj/instructorStudentPage", { params });
}

/**
 * 预警列表--预警类型统计
 * @param {*} params
 * @returns
 */
export function getAlarmWarnTypeCountAPI(params) {
  return http.get("/blade-yjxxcj/yjxxCj/warnTypeCount", { params });
}
