<!-- 教务处领导看板 -->
<template>
  <div
    class="bg-[#F8F8F8] h-full w-full box-border flex flex-col py-4 gap-4">
    <div class="px-3">
      <div
        class="bg-white rounded-lg h-11 flex items-center justify-between pl-5 pr-2.5 active:bg-[#F8F8F8]">
        <span
          class="text-[26px] text-[#555555]">2024</span>
        <IconifyIconOnline icon="ri:arrow-down-s-fill"
          class=" text-[#575B66]" width="32px"
          height="18px" />
      </div>
    </div>
    <div class="overflow-y-auto flex-1 min-h-0">
      <div class="px-3 flex flex-col gap-4">
        <!-- 预警情况 -->
        <CardItem title="预警情况" lineColor="#FF5F58"
          fromColor="#FCF0F0" toColor="#FFFFFF">
          <img :src="$getAssetsFile('yjqk-icon.png')" alt=""
            class="absolute top-0 right-2.5 w-[150px] h-[150px] z-10" />
          <div class="w-full p-3.5 box-border">
            <div class="w-full flex items-center pb-3">
              <div class="w-[220px] flex items-center justify-center">
                <van-circle
                  v-model:current-rate="currentRate"
                  :rate="80"
                  size="96px"
                  stroke-width="110"
                  layer-color="#F1F0EF"
                  :speed="50"
                  :color="{
                    '0%': '#FAB8B8',
                    '100%': '#FF4646'
                  }">
                  <div
                    class="h-full flex flex-col items-center justify-center">
                    <p class="text-[#FF0707] font-black">
                      <span class="text-[44px]">58</span>
                      <span class="text-xs">%</span>
                    </p>
                    <p class="text-xs text-[#8D8D8D]">预警率</p>
                  </div>

                </van-circle>
              </div>
              <div
                class="mx-[42px] flex-1 flex items-center justify-between">
                <div class="text-center">
                  <p class="text-xs text-[#222222]">
                    <span class="text-lg font-black">5</span>
                    <span>次</span>
                  </p>
                  <p class="mt-0.5 text-xs text-[#8D8D8D]">总预警次数</p>
                </div>
                <div class="w-0.5 h-5 bg-[#E8E8E8]"></div>
                <div class="text-center">
                  <p class="text-xs text-[#222222]">
                    <span class="text-lg font-black">5</span>
                    <span>人</span>
                  </p>
                  <p class="mt-0.5 text-xs text-[#8D8D8D]">总预警人数</p>
                </div>
              </div>
            </div>


            <div
              class="bg-[#F9F9F9] h-[128px] px-2.5 flex items-center justify-between">
              <div class="text-center">
                <p class="text-xs text-[#222222]">
                  <span class="text-lg font-black">5</span>
                  <span>次(</span>
                  <span class="text-sm">5</span>
                  <span>人)</span>
                </p>
                <p class="mt-0.5 text-xs text-[#8D8D8D]">课程成绩预警数</p>
              </div>
              <div class="w-0.5 h-5 bg-[#E8E8E8]"></div>
              <div class="text-center">
                <p class="text-xs text-[#222222]">
                  <span class="text-lg font-black">5</span>
                  <span>次(</span>
                  <span class="text-sm">5</span>
                  <span>人)</span>
                </p>
                <p class="mt-0.5 text-xs text-[#8D8D8D]">毕业设计预警数</p>
              </div>
              <div class="w-0.5 h-5 bg-[#E8E8E8]"></div>
              <div class="text-center">
                <p class="text-xs text-[#222222]">
                  <span class="text-lg font-black">5</span>
                  <span>次(</span>
                  <span class="text-sm">5</span>
                  <span>人)</span>
                </p>
                <p class="mt-0.5 text-xs text-[#8D8D8D]">顶岗实习预警数</p>
              </div>
            </div>
          </div>
        </CardItem>
        <!-- 处理情况 -->
        <CardItem title="处理情况" lineColor="#5D82F5"
          fromColor="#F0F3FC" toColor="#FFFFFF">
          <div class="h-[500px] w-full">
            <cm-echarts :option="option" v-if="option" />
          </div>
        </CardItem>
        <!-- 学生分布 -->
        <CardItem title="学生分布" lineColor="#917DF0"
          fromColor="#F4F0FC" toColor="#FFFFFF">
          <div class="h-[500px] w-full">
            <cm-echarts :option="xsfbOption" v-if="xsfbOption" />
          </div>
        </CardItem>

        <!-- 各学院预警率排序 -->
        <CardItem title="各学院预警率排序" lineColor="#FFA058"
          fromColor="#FDF4E8" toColor="#FBF8EE">
          <div class="w-full py-3.5">
            <ProgressItem />
          </div>
        </CardItem>
        <!-- 重点预警专业 TOP5 -->
        <CardItem title="重点预警专业 TOP5" lineColor="#FFA058"
          fromColor="#FDF4E8" toColor="#FBF8EE">
          <div class="px-3 py-3.75 w-full box-border ">
            <RankingProgressItem />
          </div>
        </CardItem>

        <!-- 各学院近5年预警趋势 -->
        <CardItem title="各学院近5年预警趋势" lineColor="#5D82F5"
          fromColor="#F0F3FC" toColor="#FFFFFF">
          <div class="h-[500px] w-full">
            <cm-echarts :option="yjqsOption" v-if="yjqsOption" />
          </div>
        </CardItem>
      </div>
    </div>
  </div>
</template>

<script setup>
import CmEcharts from '@/components/CmEcharts/index.vue'
import ProgressItem from './progress-item.vue'
import RankingProgressItem from './ranking-progress-item.vue'
import CardItem from './card-item.vue'
import { handlingInformationOption, singleVerageOption, brokenLineOption } from '../config'

const currentRate = ref(0)

// 处理情况
const option = handlingInformationOption(['地区1', '地区2', '地区3', '地区4', '地区5'], [220, 182, 191, 234, 290], [150, 232, 201, 154, 190], 200, 100)

// 学生分布
const xsfbOption = singleVerageOption(['地区1', '地区2', '地区3', '地区4', '地区5'], [220, 182, 191, 234, 290], 200)

//各学院近5年预警趋势
const yjqsOption = brokenLineOption(['2021', '2022', '2023', '2024', '2025'], [220, 182, 191, 234, 290], [150, 232, 201, 154, 190], [150, 232, 201, 154, 190], [150, 232, 201, 154, 190])
</script>