import http from "@cm/alova";

/**
 * @description 获取班级待办事项
 * @param {Object} params 请求参数
 * @returns {Promise<any>} 返回班级待办事项
 */
export function getClassTodoPageAPI(params) {
  return http.get("/blade-yjxxcj/yjxxCj/classTodoPage", { params });
}

/**
 * @description 获取待办事项
 * @param {Object} params 请求参数
 * @returns {Promise<any>} 返回待办事项
 */
export function getTodoPage(params) {
  return http.get("/blade-yjxxcj/yjxxCj/todoPage", { params });
}
/**
 * @description 获取待办事项数量
 * @param {Object} params 请求参数
 * @returns {Promise<any>} 返回待办事项数量
 */
export function getTodoPageCount(yjbh) {
  return http.useRequest((_params) => http.get("/blade-yjxxcj/yjxxCj/todoPageCount", { params: { ..._params, yjbh: yjbh } }), { immediate: true });
}
/**
 * @description 获取选修详情
 * @param {Object} params 请求参数
 * @returns {Promise<any>} 返回选修详情
 */
export function getXxyjDetailAPI(params) {
  return http.useRequest(() => http.get("/blade-yjxxcj/yjxxCj/xxDetail", { params }));
}

/**
 * 获取班级和专业批量处理详情
 * @param {*} params
 * @returns {Promise<any>} 返回班级批量处理详情
 */
export const classDetailAPI = (params) => {
  return http.get("/blade-yjxxcj/yjxxCj/majorClassDetail", { params });
};
/**
 * 批量处理
 * @param {*} data
 * @returns {Promise<any>} 返回批量处理结果
 */
export const handleBatchAPI = (data) => {
  return http.post("/blade-yjxxcj/yjxxCj/handle", data);
};
/**
 * 获取个人处理-选修或必修-详情
 * @param {*} paramsalova
 * @returns
 */
export const yjxxDetailAPI = (params) => {
  return http.useRequest(() => http.get("/blade-yjxxcj/yjxxCj/detail", { params }), { initialData: { yjxxCjClVO: {} }, immediate: true });
};
