<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

// Iconify在线图标示例
const onlineIcons = [
  'mdi:home',
  'mdi:account',
  'mdi:magnify',
  'mdi:heart',
  'mdi:cog',
  'mdi:calendar',
  'mdi:cart',
  'mdi:message',
]

// 返回上一页
function goBack() {
  router.back()
}
</script>

<template>
  <div class="icon-demo">
    <van-nav-bar
      title="图标使用演示"
      left-text="返回"
      left-arrow
      @click-left="goBack"
    />

    <van-cell-group inset title="Iconify在线图标">
      <van-grid :column-num="4" :border="false">
        <van-grid-item v-for="(icon, index) in onlineIcons" :key="index">
          <IconifyIconOnline
            :icon="icon"
            style="font-size: 24px; color: #1989fa;"
          />
          <div class="icon-name">
            {{ icon.split(':')[1] }}
          </div>
        </van-grid-item>
      </van-grid>
    </van-cell-group>

    <van-cell-group inset title="Iconfont图标">
      <van-grid :column-num="4" :border="false">
        <van-grid-item>
          <FontIcon
            icon="icon-home"
            style="font-size: 24px; color: #07c160;"
          />
          <div class="icon-name">
            home
          </div>
        </van-grid-item>
        <van-grid-item>
          <FontIcon
            icon="icon-user"
            style="font-size: 24px; color: #ff976a;"
          />
          <div class="icon-name">
            user
          </div>
        </van-grid-item>
        <van-grid-item>
          <FontIcon
            icon="icon-setting"
            style="font-size: 24px; color: #7232dd;"
          />
          <div class="icon-name">
            setting
          </div>
        </van-grid-item>
        <van-grid-item>
          <FontIcon
            icon="icon-search"
            style="font-size: 24px; color: #ee0a24;"
          />
          <div class="icon-name">
            search
          </div>
        </van-grid-item>
      </van-grid>
    </van-cell-group>

    <van-cell-group inset title="图标按钮示例">
      <div class="button-demo">
        <van-button type="primary" plain size="small">
          <IconifyIconOnline
            icon="mdi:plus"
            style="margin-right: 4px;"
          />
          新建
        </van-button>

        <van-button type="success" plain size="small">
          <IconifyIconOnline
            icon="mdi:check"
            style="margin-right: 4px;"
          />
          确认
        </van-button>

        <van-button type="danger" plain size="small">
          <IconifyIconOnline
            icon="mdi:delete"
            style="margin-right: 4px;"
          />
          删除
        </van-button>
      </div>
    </van-cell-group>

    <van-cell-group inset title="带徽标的图标">
      <div class="badge-demo">
        <van-badge :content="5" max="99">
          <IconifyIconOnline
            icon="mdi:bell"
            style="font-size: 28px; color: #1989fa;"
          />
        </van-badge>

        <van-badge dot>
          <IconifyIconOnline
            icon="mdi:email"
            style="font-size: 28px; color: #07c160;"
          />
        </van-badge>
      </div>
    </van-cell-group>

    <van-cell-group inset title="尺寸变化示例">
      <div class="size-demo">
        <IconifyIconOnline
          icon="mdi:star"
          style="font-size: 16px; color: #ff976a;"
        />
        <IconifyIconOnline
          icon="mdi:star"
          style="font-size: 24px; color: #ff976a;"
        />
        <IconifyIconOnline
          icon="mdi:star"
          style="font-size: 32px; color: #ff976a;"
        />
        <IconifyIconOnline
          icon="mdi:star"
          style="font-size: 48px; color: #ff976a;"
        />
      </div>
    </van-cell-group>
  </div>
</template>

<style scoped>
.icon-demo {
  padding-bottom: 20px;
}

.icon-name {
  font-size: 12px;
  margin-top: 4px;
  color: #646566;
}

.button-demo {
  display: flex;
  gap: 12px;
  padding: 16px;
}

.badge-demo {
  display: flex;
  gap: 24px;
  padding: 16px;
}

.size-demo {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
}
</style>
