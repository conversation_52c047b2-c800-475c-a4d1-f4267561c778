<script setup>
import { useRouter } from 'vue-router'

definePage({
  meta: {
    title: '404',
    isAuth: false,
  },
})
const router = useRouter()

function goHome() {
  router.push('/')
}
</script>

<template>
  <div class="scene bg-[#f8fafc] dark:bg-dark">
    <div class="error-content">
      <div class="number-container">
        <div class="number four">
          4
        </div>
        <div class="astronaut-container">
          <div class="astronaut">
            <div class="astronaut-body">
              <div class="astronaut-helmet">
                <div class="astronaut-glass" />
              </div>
              <div class="astronaut-suit" />
            </div>
            <div class="astronaut-hand-left" />
            <div class="astronaut-hand-right" />
            <div class="astronaut-foot-left" />
            <div class="astronaut-foot-right" />
          </div>
        </div>
        <div class="number four">
          4
        </div>
      </div>
      <h1 class="title text-[#64748b] dark:text-white">
        页面未找到
      </h1>
      <p class="message text-[#64748b] dark:text-white">
        抱歉，您访问的页面不存在或已被移除
      </p>
      <button class="home-button" @click="goHome">
        返回首页
      </button>
    </div>
  </div>
</template>

<style scoped>
.scene {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 1000px;
  overflow: hidden;
  padding: 32px;
}

.error-content {
  transform-style: preserve-3d;
  padding: 64px;
}

.number-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 32px;
  margin-bottom: 64px;
  transform-style: preserve-3d;
}

.number {
  font-size: 256px;
  font-weight: 800;
  color: #334155;
  text-shadow:
    2px 2px 0 #94a3b8,
    4px 4px 0 #64748b;
  transform: translateZ(50px);
  animation: float 6s ease-in-out infinite;
}

.number:last-child {
  animation-delay: -3s;
}

.astronaut-container {
  transform-style: preserve-3d;
  transform: translateZ(80px);
  animation: float 6s ease-in-out infinite;
  animation-delay: -1.5s;
}

.astronaut {
  position: relative;
  width: 120px;
  height: 120px;
  animation: spin 12s linear infinite;
}

.astronaut-body {
  position: absolute;
  width: 60px;
  height: 90px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(to bottom, #0ea5e9, #a207f0);
  border-radius: 40px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.astronaut-helmet {
  position: absolute;
  width: 48px;
  height: 48px;
  left: 50%;
  top: -10px;
  transform: translateX(-50%);
  background: linear-gradient(to bottom, #0ea5e9, #a207f0);
  border-radius: 50%;
  border: 4px solid #e2e8f0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.astronaut-glass {
  position: absolute;
  width: 32px;
  height: 32px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(to bottom, #0ea5e9, #a207f0);
  border-radius: 50%;
  border: 3px solid #e2e8f0;
  animation: glass-shine 3s ease-in-out infinite;
  overflow: hidden;
}

.astronaut-glass::after {
  content: '';
  position: absolute;
  width: 150%;
  height: 150%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: translateX(-200%);
  animation: glass-sweep 3s ease-in-out infinite;
}

.astronaut-hand-left,
.astronaut-hand-right {
  position: absolute;
  width: 25px;
  height: 25px;
  background: linear-gradient(to bottom, #0ea5e9, #a207f0);
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.astronaut-hand-left {
  left: -5px;
  top: 50%;
  animation: wave 2s ease-in-out infinite;
}

.astronaut-hand-right {
  right: -5px;
  top: 50%;
  animation: wave 2s ease-in-out infinite reverse;
}

.astronaut-foot-left,
.astronaut-foot-right {
  position: absolute;
  width: 25px;
  height: 25px;
  background: linear-gradient(to bottom, #0ea5e9, #a207f0);
  border-radius: 10px;
  bottom: -10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.astronaut-foot-left {
  left: 15px;
}

.astronaut-foot-right {
  right: 15px;
}

.title {
  font-size: 80px;
  margin-bottom: 32px;
  font-weight: 700;
  transform: translateZ(30px);
}

.message {
  font-size: 35px;
  margin-bottom: 64px;
  transform: translateZ(20px);
}

.home-button {
  display: inline-flex;
  align-items: center;
  padding: 16px 64px;
  font-size: 35px;
  color: white;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  transform: translateZ(40px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.home-button:hover {
  transform: translateZ(40px) translateY(-2px);
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.4);
}

@keyframes wave {
  0%,
  100% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(20deg);
  }
}

@keyframes glass-shine {
  0%,
  100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

@keyframes glass-sweep {
  0% {
    transform: translateX(-200%) rotate(45deg);
  }
  50% {
    transform: translateX(100%) rotate(45deg);
  }
  100% {
    transform: translateX(-200%) rotate(45deg);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateZ(50px) translateY(0);
  }
  50% {
    transform: translateZ(50px) translateY(-20px);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes heartbeat {
  0%,
  100% {
    transform: translate(-100%, -50%) rotate(-45deg) scale(1);
  }
  50% {
    transform: translate(-100%, -50%) rotate(-45deg) scale(1.1);
  }
}

@media (max-width: 640px) {
  .number {
    font-size: 192px;
  }

  .astronaut {
    width: 90px;
    height: 90px;
  }

  .astronaut-body {
    width: 45px;
    height: 67.5px;
  }

  .astronaut-helmet {
    width: 36px;
    height: 36px;
  }

  .astronaut-glass {
    width: 24px;
    height: 24px;
  }

  .title {
    font-size: 64px;
  }

  .message {
    font-size: 32px;
  }
}
</style>
