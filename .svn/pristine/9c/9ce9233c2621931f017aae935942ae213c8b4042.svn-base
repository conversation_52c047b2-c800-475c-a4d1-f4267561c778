# =============================================================================
# Nginx 配置文件
# 
# 这个配置文件针对 Vue SPA 应用进行了优化，包含：
# 1. 静态资源服务和缓存策略
# 2. SPA 路由支持（History Mode）
# 3. API 代理配置
# 4. Gzip 压缩优化
# 5. 安全头设置
# 6. 性能优化配置
# =============================================================================

# -----------------------------------------------------------------------------
# 全局配置
# -----------------------------------------------------------------------------
# 工作进程数，auto 表示自动检测 CPU 核心数
worker_processes auto;

# 错误日志配置
error_log /var/log/nginx/error.log warn;

# 进程 ID 文件
pid /var/run/nginx.pid;

# -----------------------------------------------------------------------------
# 事件配置
# -----------------------------------------------------------------------------
events {
    # 每个工作进程的最大连接数
    worker_connections 1024;
    
    # 使用高效的事件驱动模型
    use epoll;
    
    # 允许一个工作进程同时处理多个连接
    multi_accept on;
}

# -----------------------------------------------------------------------------
# HTTP 配置
# -----------------------------------------------------------------------------
http {
    # 包含 MIME 类型配置
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # -------------------------------------------------------------------------
    # 日志格式配置
    # -------------------------------------------------------------------------
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    # 访问日志配置
    access_log /var/log/nginx/access.log main;

    # -------------------------------------------------------------------------
    # 性能优化配置
    # -------------------------------------------------------------------------
    # 开启高效文件传输
    sendfile on;
    
    # 优化 sendfile 包
    tcp_nopush on;
    tcp_nodelay on;
    
    # 连接超时时间
    keepalive_timeout 65;
    
    # 客户端请求体大小限制
    client_max_body_size 10M;
    
    # 隐藏 Nginx 版本号（安全考虑）
    server_tokens off;

    # -------------------------------------------------------------------------
    # Gzip 压缩配置
    # -------------------------------------------------------------------------
    # 启用 Gzip 压缩，显著减少传输数据量
    gzip on;
    
    # 压缩级别（1-9，数值越高压缩率越高但 CPU 消耗越大）
    gzip_comp_level 6;
    
    # 小于 1KB 的文件不压缩（压缩效果不明显）
    gzip_min_length 1000;
    
    # 压缩的文件类型
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 为代理请求启用压缩
    gzip_proxied any;
    
    # 添加 Vary: Accept-Encoding 头
    gzip_vary on;

    # -------------------------------------------------------------------------
    # 服务器配置
    # -------------------------------------------------------------------------
    server {
        # 监听 9977 端口
        listen 9977;
        
        # 服务器名称（在生产环境中应该设置为实际域名）
        server_name localhost;
        
        # 网站根目录
        root /usr/share/nginx/html;
        
        # 默认首页文件
        index index.html;

        # ---------------------------------------------------------------------
        # 安全头配置
        # ---------------------------------------------------------------------
        # 防止点击劫持攻击
        add_header X-Frame-Options "SAMEORIGIN" always;
        
        # 防止 MIME 类型嗅探
        add_header X-Content-Type-Options "nosniff" always;
        
        # XSS 保护
        add_header X-XSS-Protection "1; mode=block" always;
        
        # 引用策略
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;

        # ---------------------------------------------------------------------
        # 静态资源缓存策略
        # ---------------------------------------------------------------------
        # 对于带有哈希值的静态资源（JS、CSS、图片等），设置长期缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            # 缓存 1 年
            expires 1y;
            add_header Cache-Control "public, immutable";
            
            # 启用 etag
            etag on;
            
            # 如果文件不存在，返回 404
            try_files $uri =404;
        }
        
        # HTML 文件不缓存，确保更新能及时生效
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
        }

        # ---------------------------------------------------------------------
        # SPA 路由支持
        # ---------------------------------------------------------------------
        # Vue Router History 模式支持
        # 所有不匹配静态文件的请求都返回 index.html
        location / {
            try_files $uri $uri/ /index.html;
            
            # 为 index.html 设置不缓存
            location = /index.html {
                expires -1;
                add_header Cache-Control "no-cache, no-store, must-revalidate";
                add_header Pragma "no-cache";
            }
        }

        # ---------------------------------------------------------------------
        # 健康检查端点
        # ---------------------------------------------------------------------
        # 用于 Docker 健康检查和负载均衡器健康检查
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # ---------------------------------------------------------------------
        # 错误页面配置
        # ---------------------------------------------------------------------
        # 自定义错误页面
        error_page 404 /index.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /usr/share/nginx/html;
        }

        # ---------------------------------------------------------------------
        # 安全配置
        # ---------------------------------------------------------------------
        # 禁止访问隐藏文件
        location ~ /\. {
            deny all;
        }
        
        # 禁止访问备份文件
        location ~ ~$ {
            deny all;
        }
    }
}

# =============================================================================
# 配置说明：
# 
# 1. 这个配置适用于 Vue SPA 应用的生产环境
# 2. 包含了性能优化、安全加固、缓存策略等最佳实践
# 3. API 代理部分需要根据实际后端服务地址进行调整
# 4. 可以根据实际需求调整缓存时间和压缩设置
# 
# 重要提醒：
# - 在生产环境中，请将 server_name 设置为实际域名
# - 根据后端服务的实际地址修改 proxy_pass 配置
# - 考虑启用 HTTPS 和添加 SSL 配置
# - 定期检查和更新安全头配置
# ============================================================================= 