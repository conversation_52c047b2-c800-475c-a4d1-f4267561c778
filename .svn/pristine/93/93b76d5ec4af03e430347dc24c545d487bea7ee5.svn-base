/**
 * 树遍历器模块
 * 提供深度优先和广度优先遍历功能
 */

/**
 * 深度优先遍历树结构
 * @param {Array|Object} tree - 树结构数据
 * @param {Function} callback - 回调函数 (node, level, path, parent) => void
 * @param {Object} [options={}] - 配置选项
 * @param {string} [options.childrenKey='children'] - 子节点字段名
 * @param {number} [options.level=0] - 当前层级
 * @param {Array} [options.path=[]] - 当前路径
 * @param {Object} [options.parent=null] - 父节点
 */
export function traverseDepthFirst(tree, callback, options = {}) {
  if (!tree) {
    return
  }

  const config = {
    childrenKey: options.childrenKey || 'children',
    level: options.level || 0,
    path: options.path || [],
    parent: options.parent || null,
  }

  // 处理单个节点
  if (!Array.isArray(tree)) {
    tree = [tree]
  }

  for (const [index, node] of tree.entries()) {
    if (!node) continue

    const currentPath = [...config.path, index]
    
    // 执行回调
    if (typeof callback === 'function') {
      callback(node, config.level, currentPath, config.parent)
    }

    // 递归处理子节点
    const children = node[config.childrenKey]
    if (children && Array.isArray(children) && children.length > 0) {
      traverseDepthFirst(children, callback, {
        ...options,
        level: config.level + 1,
        path: currentPath,
        parent: node,
      })
    }
  }
}

/**
 * 广度优先遍历树结构
 * @param {Array|Object} tree - 树结构数据
 * @param {Function} callback - 回调函数 (node, level, index, parent) => void
 * @param {Object} [options={}] - 配置选项
 * @param {string} [options.childrenKey='children'] - 子节点字段名
 */
export function traverseBreadthFirst(tree, callback, options = {}) {
  if (!tree) {
    return
  }

  const config = {
    childrenKey: options.childrenKey || 'children',
  }

  // 处理单个节点
  if (!Array.isArray(tree)) {
    tree = [tree]
  }

  // 使用队列进行广度优先遍历
  const queue = tree.map((node, index) => ({
    node,
    level: 0,
    index,
    parent: null,
  }))

  while (queue.length > 0) {
    const { node, level, index, parent } = queue.shift()

    if (!node) continue

    // 执行回调
    if (typeof callback === 'function') {
      callback(node, level, index, parent)
    }

    // 将子节点加入队列
    const children = node[config.childrenKey]
    if (children && Array.isArray(children) && children.length > 0) {
      children.forEach((child, childIndex) => {
        queue.push({
          node: child,
          level: level + 1,
          index: childIndex,
          parent: node,
        })
      })
    }
  }
}

/**
 * 提取树中所有节点的指定字段值
 * @param {Array|Object} tree - 树结构数据
 * @param {string} fieldKey - 要提取的字段名
 * @param {Object} [options={}] - 配置选项
 * @param {string} [options.childrenKey='children'] - 子节点字段名
 * @returns {Array} 字段值数组
 */
export function extractFieldValues(tree, fieldKey, options = {}) {
  if (!tree || !fieldKey) {
    return []
  }

  const values = []
  
  traverseDepthFirst(tree, (node) => {
    if (node && node.hasOwnProperty(fieldKey)) {
      values.push(node[fieldKey])
    }
  }, options)

  return values
}

/**
 * 提取路径列表（兼容原有API）
 * @param {Array} tree - 树结构数据
 * @param {Object} [options={}] - 配置选项
 * @param {string} [options.fieldKey='uniqueId'] - 要提取的字段名
 * @param {string} [options.childrenKey='children'] - 子节点字段名
 * @returns {Array} 路径值数组
 */
export function extractPathList(tree, options = {}) {
  const config = {
    fieldKey: options.fieldKey || 'uniqueId',
    childrenKey: options.childrenKey || 'children',
  }

  return extractFieldValues(tree, config.fieldKey, {
    childrenKey: config.childrenKey,
  })
}

/**
 * 获取树的统计信息
 * @param {Array|Object} tree - 树结构数据
 * @param {Object} [options={}] - 配置选项
 * @param {string} [options.childrenKey='children'] - 子节点字段名
 * @returns {Object} 统计信息
 */
export function getTreeStats(tree, options = {}) {
  if (!tree) {
    return {
      totalNodes: 0,
      maxDepth: 0,
      leafNodes: 0,
      branchNodes: 0,
    }
  }

  const config = {
    childrenKey: options.childrenKey || 'children',
  }

  let totalNodes = 0
  let maxDepth = 0
  let leafNodes = 0
  let branchNodes = 0

  traverseDepthFirst(tree, (node, level) => {
    totalNodes++
    maxDepth = Math.max(maxDepth, level + 1)

    const children = node[config.childrenKey]
    if (!children || !Array.isArray(children) || children.length === 0) {
      leafNodes++
    } else {
      branchNodes++
    }
  }, options)

  return {
    totalNodes,
    maxDepth,
    leafNodes,
    branchNodes,
  }
}

// 默认导出主要函数
export default {
  traverseDepthFirst,
  traverseBreadthFirst,
  extractFieldValues,
  extractPathList,
  getTreeStats,
}
