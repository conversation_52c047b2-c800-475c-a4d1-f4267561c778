{
  "oxc.enable": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.oxc": "explicit" // 保存时执行 OXC 的“修复所有”：仅在显式触发保存时执行（而非自动保存）
  },
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode" // HTML 默认用 Prettier 格式化
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode" // CSS 默认用 Prettier 格式化
  },
  "[less]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode" // Less 默认用 Prettier 格式化
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode" // JS 默认用 Prettier 格式化
  },
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode" // Vue 默认用 Prettier（通常与 Volar 协同）
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsx-tags]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
