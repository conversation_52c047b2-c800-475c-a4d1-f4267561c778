import storage from '@cm/utils/storage'
import { accessTokenKey, refreshTokenKey } from '@/config/website'

/**
 * Token管理服务
 * 提供token的获取、缓存和清理功能
 */
class TokenService {
  constructor() {
    this.tokenCache = null
    this.cacheExpiration = 0
    this.cacheDuration = 5 * 60 * 1000 // 5分钟缓存
  }

  /**
   * 获取用户token（带缓存机制）
   * @returns {Promise<string|null>} 用户token
   */
  async getUserToken() {
    // 检查缓存是否有效
    if (this.tokenCache && Date.now() < this.cacheExpiration) {
      return this.tokenCache
    }

    try {
      // 从storage获取token
      const accessToken = await storage.get(accessTokenKey)
      const refreshToken = await storage.get(refreshTokenKey)
      const token = accessToken || refreshToken

      // 更新缓存
      if (token) {
        this.tokenCache = token
        this.cacheExpiration = Date.now() + this.cacheDuration
      }
      else {
        this.clearCache()
      }

      return token
    }
    catch (error) {
      console.error('获取token失败:', error)
      this.clearCache()
      return null
    }
  }

  /**
   * 检查用户是否已登录
   * @returns {Promise<boolean>} 是否已登录
   */
  async isAuthenticated() {
    const token = await this.getUserToken()
    return Boolean(token)
  }

  /**
   * 清理token缓存
   */
  clearCache() {
    this.tokenCache = null
    this.cacheExpiration = 0
  }

  /**
   * 清理所有token数据
   */
  async clearAll() {
    this.clearCache()
    await storage.clear()
  }
}

// 导出单例实例
export default new TokenService()
