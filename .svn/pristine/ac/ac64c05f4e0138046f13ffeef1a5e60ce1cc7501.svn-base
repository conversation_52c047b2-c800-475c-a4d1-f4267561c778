import http from "@cm/alova";
/**
 * @description 获取院校列表
 * @returns {Promise<any>} 返回院校列表
 */

const cacheFor = {
  // 设置缓存模式为持久化模式
  mode: "restore",
  // 缓存时间
  expire: 24 * 60 * 60 * 1000,
};
export function getYxbListAPI(params) {
  return http.useRequest(
    () =>
      http.request({
        url: "/blade-yxb/Yxb/list",
        params,
        cacheFor,
        transform(rawData) {
          return rawData.data.map((item) => {
            return {
              ...item,
              text: item.name,
              value: item.code,
            };
          });
        },
      }),
    {
      immediate: false,
    },
  );
}

/**
 * @description 获取专业列表
 * @returns {Promise<any>} 返回专业列表
 */
export function getZyszsjListAPI(params) {
  return http.useRequest(
    () =>
      http.request({
        url: "/blade-zyszsj/zyszsj/list",
        params,
        cacheFor,
        transform(rawData) {
          return rawData.data.map((item) => {
            return {
              ...item,
              text: item.zymc,
              value: item.zyh,
            };
          });
        },
      }),
    {
      immediate: false,
    },
  );
}

/**
 * @description 获取年级列表
 * @returns {Promise<any>} 返回年级列表
 */
export function getNjListAPI(params) {
  return http.useRequest(
    () =>
      http.request({
        url: "/blade-bjsj/Bjsj/njList",
        params,
        cacheFor,
        transform(rawData) {
          return rawData.data.map((item) => {
            return {
              text: item.ssnj,
              value: item.ssnj,
            };
          });
        },
      }),
    {
      immediate: false,
    },
  );
}

/**
 * @description 获取班级列表
 * @returns {Promise<any>} 返回班级列表
 */
export function getBjListAPI(params) {
  return http.useRequest(
    () =>
      http.request({
        url: "/blade-bjsj/Bjsj/list",
        params,
        cacheFor,
        transform(rawData) {
          return rawData.data.map((item) => {
            return {
              text: item.bjmc,
              value: item.bh,
            };
          });
        },
      }),
    {
      immediate: false,
    },
  );
}
