import http from "@cm/alova";
import { encrypt } from "@/utils/sm2";

/*
 * @description 登录
 * @param {string} username 用户名
 * @param {string} password 密码
 * @returns {Promise<any>} 返回登录信息
 */
export function loginByUsername(username, password) {
  return http.post("/blade-auth/oauth/token", void 0, {
    params: {
      username,
      password: encrypt(password),
      grant_type: "password",
      scope: "all",
      tenantId: "000000",
      type: "account",
    },
    meta: {
      authRole: "login",
    },
  });
}

/*
 * @description 登录
 * @param {string} username 用户名
 * @param {string} password 密码
 * @returns {Promise<any>} 返回登录信息
 */
export function loginByCas() {
  return http.post("/cas/login", void 0, {
    meta: {
      authRole: "login",
    },
  });
}

/**
 * @description 获取路由
 * @returns {Promise<any>} 返回路由配置信息
 */
export function getRouters(id) {
  return http.get("/blade-system/menu/routes", id ? { id } : undefined);
}

/**
 * @description 获取用户信息
 * @returns {Promise<any>} 返回用户信息
 */
export function getUserInfoAPI() {
  return http.useRequest(
    () =>
      http.request({
        url: "/blade-system/user/info",
        params: { id: "123" },
        cacheFor: 10 * 60 * 1000,
      }),
    { immediate: false },
  );
}

export function refreshTokenAPI(refreshToken) {
  return http.request({
    url: "/blade-auth/oauth/token",
    method: "post",
    immediate: true,
    params: {
      tenantId: "000000",
      grant_type: "refresh_token",
      refresh_token: refreshToken,
    },
    meta: {
      authRole: "refreshToken",
    },
  });
}

/**
 * @description 上传文件
 * @param {File} file 文件
 * @returns {Promise<any>} 返回文件信息
 */
export function uploadFileAPI() {
  return http.useUploader(({ file, name }) => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("name", name);
    return http.request({
      url: "/blade-resource/oss/endpoint/put-file-attach",
      method: "post",
      data: formData,
    });
  });
}

export function getUserAPI(params, watcherState = []) {
  return http.useWatcher(
    () =>
      http.request({
        url: "/blade-system/user/info1",
        params: { id: params.value },
      }),
    watcherState,
    { debounce: 500 },
  );
}
