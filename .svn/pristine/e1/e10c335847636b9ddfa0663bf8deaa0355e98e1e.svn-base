<template>
  <div class="ssw-todo h-full w-full flex flex-col">
    <div class="bg-white">
      <div class="relative h-[88px] flex items-center">
        <p class="flex-1 h-full box-border text-center pt-[20px] text-[24px] text-[#BFBFBF] font-normal font-pingfang" :class="{ '!text-[#232528]': activeTab === '1' }" @click="activeTab = '1'">
          待办
        </p>
        <p class="flex-1 h-full box-border text-center pt-[20px] text-[24px] text-[#BFBFBF] font-normal font-pingfang" :class="{ '!text-[#222]': activeTab === '2' }" @click="activeTab = '2'">已办</p>
        <img
          :src="$getAssetsFile('tabs-active-icon.png')"
          alt=""
          class="w-3 h-1.5 absolute left-[25%] bottom-1.5 translate-x-[-50%] transition-all duration-300"
          :class="{ '!left-[75%]': activeTab === '2' }" />
      </div>
      <p class="bg-[#FFF8F0] p-2.5 text-[#EC8800] text-[23px] font-normal font-pingfang">【温馨提示】以下预警信息基于教务系统产生，最终解释权在教务处。</p>
      <SearchItem v-model:searchParams="searchParams" :options="searchOptions" />
      <TabsItem :options="tabsOptions" :countData="todoPageCount" v-model:businessActive="businessActive" />
    </div>

    <div class="flex-1 min-h-0 py-[24px]">
      <div class="h-full overflow-y-auto px-[26px]">
        <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div class="flex flex-col gap-y-[30px]">
            <ListItem v-for="item in list" :key="item" :options="listOptionsKey" btnName="处理" :data="item">
              <template #title>
                <span>预警学生：{{ item.xsxm }}({{ item.xsxh }})</span>
              </template>
              <template #kcmc="{ data }">
                <span class="text-[#222] text-[24px] font-normal flex-1">{{ data.kcxnh }}/{{ data.kcxqh }}</span>
              </template>
            </ListItem>
          </div>
        </van-list>
      </div>
    </div>
  </div>
</template>

<script setup>
import SearchItem from "@/views/todo/components/search-item.vue";
import TabsItem from "@/views/todo/components/tabs-item.vue";
import ListItem from "./components/list-item.vue";
import { getTodoPage, getTodoPageCount, getDonePageCountAPI } from "@/api/todo";
defineOptions({
  name: "TodoStudent",
});
definePage({
  name: "TodoStudent",
  meta: {
    layout: "index",
    title: "待办",
    navBar: true,
    isAuth: false,
  },
});
const route = useRoute();

const activeTab = ref("1");

// 搜索参数
const searchParams = ref({
  grade: "",
  class: "",
});

// 搜索参数选项

const searchOptions = ref([
  {
    key: "nj", //获取选择数据源
    label: "年级",
    value: "grade",
    type: "select",
    placeholder: "全部年级",
  },
  {
    key: "bj",
    label: "班级",
    value: "class",
    type: "select",
    placeholder: "全部班级",
  },
  {
    key: "xm",
    label: "姓名",
    value: "name",
    type: "input",
    placeholder: "搜索姓名",
  },
]);

// 业务类型选择
const todoPageCount = ref({});
const { data: todoPageCountData, send: sendTodoPageCount } = getTodoPageCount({ yjbh: route.query.yjbh });
const { data: donePageCountData, send: sendDonePageCount } = getDonePageCountAPI({ yjbh: route.query.yjbh });
watch(
  activeTab,
  (newVal) => {
    if (newVal === "1") {
      sendTodoPageCount();
      todoPageCount.value = todoPageCountData;
    } else {
      sendDonePageCount();
      todoPageCount.value = donePageCountData;
    }
  },
  { immediate: true },
);
const businessActive = ref("bx");
const listOptionsKey = computed(() => {
  return Reflect.get(listOptions.value, businessActive.value);
});
// 业务类型选择选项
const tabsOptions = ref([
  {
    label: "必修",
    value: "1",
    key: "bx",
    tabsCountKey: "cjyjsl",
  },
  {
    label: "选修",
    value: "2",
    key: "xx",
    tabsCountKey: "xxyjsl",
  },
  {
    label: "实习",
    value: "3",
    key: "sx",
    tabsCountKey: "dgsxyjsl",
  },
  {
    label: "毕设",
    value: "4",
    key: "bs",
    tabsCountKey: "bysjyjsl",
  },
]);

// 列表选项
const listOptions = ref({
  bx: [
    {
      label: "课程名称",
      value: "kcmc",
    },
    {
      label: "开课学年/学期",
      slot: "kcmc",
    },
    {
      label: "实得成绩",
      value: "sdcj",
    },
    {
      label: "成绩类型",
      value: "cjlxmc",
    },
  ],
  xx: [
    {
      label: "已修课程",
      value: "value",
    },
    {
      label: "待修课程",
      value: "value",
    },
  ],
});

const list = ref([]);
const loading = ref(false);
const finished = ref(false);
const pageCurrent = ref(1);
const onLoad = () => {
  getTodoPage({
    current: pageCurrent.value,
    pageSize: 10,
    ...searchParams.value,
  }).then((res) => {
    if (pageCurrent.value === 1) {
      list.value = res.records;
    } else {
      list.value.push(...res.records);
    }
    // 加载状态结束
    loading.value = false;
    if (res.total <= list.value.length) {
      finished.value = true;
    } else {
      pageCurrent.value++;
    }
  });
};
const onRefresh = () => {
  // 清空列表数据
  finished.value = false;
  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  onLoad();
};

watch([activeTab, businessActive], (newVal) => {
  list.value = [];
  onRefresh();
});
</script>

<style lang="scss" scoped>
.ssw-todo {
  background: #f0f2f9;
  font-size: 14px;
  .business-tabs {
    &::after {
      content: "";
      position: absolute;
      left: var(--indicator-left, 12.5%);
      bottom: 0;
      width: 24px;
      height: 4px;
      background: #0e5fff;
      transform: translateX(-50%);
      transition: all 0.3s ease-in-out;
    }
  }
}
</style>
