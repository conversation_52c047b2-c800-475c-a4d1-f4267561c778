/**
 * @cm/tree-utils - 树结构数据处理工具库
 * 
 * 提供构建、遍历、操作树形数据的完整解决方案
 */

// 导入构建器
import {
  buildTreeFromFlat,
  buildHierarchyTree,
  optimizeTreeStructure,
  handleTree,
} from './builders.js'

// 导入遍历器
import {
  traverseDepthFirst,
  traverseBreadthFirst,
  extractFieldValues,
  extractPathList,
  getTreeStats,
} from './traversal.js'

// 导入操作器
import {
  findNode,
  getNodeByUniqueId,
  filterTree,
  mapTree,
  appendFieldByUniqueId,
  getNodePath,
} from './manipulation.js'

// 导出所有功能
export {
  // 构建器
  buildTreeFromFlat,
  buildHierarchyTree,
  optimizeTreeStructure,
  handleTree,

  // 遍历器
  traverseDepthFirst,
  traverseBreadthFirst,
  extractFieldValues,
  extractPathList,
  getTreeStats,

  // 操作器
  findNode,
  getNodeByUniqueId,
  filterTree,
  mapTree,
  appendFieldByUniqueId,
  getNodePath,
}

// 兼容原有API的导出（别名）
export {
  optimizeTreeStructure as deleteChildren,
}

// 默认导出（向后兼容）
export default {
  // 构建器
  buildTreeFromFlat,
  buildHierarchyTree,
  optimizeTreeStructure,
  handleTree,

  // 遍历器
  traverseDepthFirst,
  traverseBreadthFirst,
  extractFieldValues,
  extractPathList,
  getTreeStats,

  // 操作器
  findNode,
  getNodeByUniqueId,
  filterTree,
  mapTree,
  appendFieldByUniqueId,
  getNodePath,

  // 兼容性别名
  deleteChildren: optimizeTreeStructure,
}
