{"Vue3.3+Index页面模板": {"scope": "vue", "prefix": "vue3-index针对index.vue", "body": ["<template>", "\t<div>${TM_DIRECTORY/^.*[\\\\\\/]([^\\\\\\/]+)$/${1:/pascalcase}/}</div>", "</template>\n", "<script setup>", "defineOptions({", "\tname: '${TM_DIRECTORY/^.*[\\\\\\/]([^\\\\\\/]+)$/${1:/pascalcase}/}'", "})", "definePage({", "\tname: '${TM_DIRECTORY/^.*[\\\\\\/]([^\\\\\\/]+)$/${1:/pascalcase}/}',", "\tmeta: {", "\t\tlayout: 'index',", "\t\ttitle: '${1:页面标题}',", "\t\tnavBar: true,", "\t\tisAuth: false,", "\t}", "})", "</script>\n", "<style lang='scss' scoped>\n", "</style>", "$2"], "description": "Vue3.3+Index页面模板 - 使用父目录名作为组件名（适用于index.vue文件）"}}