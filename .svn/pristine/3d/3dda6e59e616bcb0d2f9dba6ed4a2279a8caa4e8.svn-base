import { showNotify } from 'vant'

/**
 * 全局代码错误捕捉
 * 比如 null.length 就会被捕捉到
 *
 * 增强版本：支持过滤本地错误，防止重复处理
 */
export default (error, vm) => {
  // 过滤HTTP请求错误
  if (error.status) {
    return false
  }

  // 过滤标记为本地处理的错误（如文件上传错误）
  // 这些错误应该只在本地 try-catch 块中处理，不触发全局错误处理器
  if (error.isLocalError || error.skipGlobalHandler) {
    console.warn('[GlobalErrorHandler] 跳过本地错误处理:', error.name, error.message)
    return false
  }

  // 过滤特定的上传错误类型
  if (error.name === 'UploadError') {
    console.warn('[GlobalErrorHandler] 跳过上传错误处理:', error.name, error.message)
    return false
  }

  const errorMap = {
    InternalError: 'Javascript引擎内部错误',
    ReferenceError: '未找到对象',
    TypeError: '使用了错误的类型或对象',
    RangeError: '使用内置对象时，参数超范围',
    SyntaxError: '语法错误',
    EvalError: '错误的使用了Eval',
    URIError: 'URI错误',
  }

  const errorName = errorMap[error.name] || '未知错误'
  console.error('[GlobalErrorHandler] 处理全局错误:', error)

  // 使用 Vue 3 的 nextTick
  if (vm && vm.$nextTick) {
    vm.$nextTick(() => {
      showNotify({ type: 'danger', message: errorName })
    })
  } else {
    // 如果没有 vm 实例，直接显示通知
    showNotify({ type: 'danger', message: errorName })
  }
}
