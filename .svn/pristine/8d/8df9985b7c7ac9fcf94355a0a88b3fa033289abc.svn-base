import { ROUTE_META, SCROLL_BEHAVIOR } from "../constants";

/**
 * 导航相关路由守卫
 */

/**
 * 设置页面标题
 * @param {object} to - 目标路由对象
 */
export function setDocumentTitle(to) {
  const { VITE_TITLE } = import.meta.env;
  const title = to.meta?.[ROUTE_META.TITLE];

  if (VITE_TITLE && title) {
    document.title = `${VITE_TITLE}-${title}`;
  } else if (title) {
    document.title = title;
  }
}

/**
 * 导航守卫 - 处理页面标题设置
 * @param {object} to - 目标路由对象
 * @param {object} _from - 来源路由对象
 * @returns {boolean} 是否需要继续执行后续守卫
 */
export function navigationGuard(to, _from) {
  // 设置页面标题
  setDocumentTitle(to);
  return true; // 继续执行后续守卫
}

/**
 * 滚动行为处理函数
 * @param {object} to - 目标路由对象
 * @param {object} from - 来源路由对象
 * @param {object|null} savedPosition - 保存的位置
 * @returns {Promise<{left: number, top: number}>} 滚动位置
 */
export function scrollBehaviorHandler(to, from, savedPosition) {
  return new Promise((resolve) => {
    try {
      if (savedPosition) {
        // 如果有保存的位置，恢复到该位置
        resolve(savedPosition);
      } else if (from.meta?.[ROUTE_META.SAVE_SCROLL_TOP]) {
        // 如果来源路由需要保存滚动位置
        const top = document.documentElement.scrollTop || document.body.scrollTop;
        resolve({ left: SCROLL_BEHAVIOR.LEFT, top });
      } else {
        // 默认滚动到顶部
        resolve(SCROLL_BEHAVIOR.TOP);
      }
    } catch (error) {
      console.error("滚动行为处理失败:", error);
      // 出错时默认滚动到顶部
      resolve(SCROLL_BEHAVIOR.TOP);
    }
  });
}

/**
 * 处理路由跳转加载状态
 * @param {object} _to - 目标路由对象
 * @param {object} _from - 来源路由对象
 */
export function handleRouteLoading(_to, _from) {
  // 可以在这里添加全局加载状态的处理
  // 例如：showLoading()
}

/**
 * 处理路由跳转完成
 * @param {object} _to - 目标路由对象
 */
export function handleRouteComplete(_to) {
  // 可以在这里添加路由跳转完成后的处理
  // 例如：hideLoading()
}
