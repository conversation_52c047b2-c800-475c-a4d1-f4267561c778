/**
 * 全局配置文件
 */
const website = {
  clientId: "saber", // 客户端id
  clientSecret: "saber_secret", // 客户端密钥
  tenantId: "000000", // 管理组租户编号
  tokenHeader: "Blade-Auth",
  tokenType: "bearer",
  key: "cm", // 本地保存的前缀key
  loginExpired: "/login", // 登录地址
  // 是否开启无缝刷新TOKEN
  isRefreshToken: true,
  // TOKEN字段
  accessTokenKey: "access_token",
  // 刷新TOKEN字段
  refreshTokenKey: "refresh_token",
  fistPage: {
    name: "首页",
    path: "/todo/todo-class",
  },
  // oauth2配置
  oauth2: {
    // 是否开启注册功能
    registerMode: false,
    // 使用后端工程 @org.springblade.test.Sm2KeyGenerator 获取
    publicKey: "0401b98082854bf47e7533fcdb204e13e11ea3aaed640accfca19550c950bc89e96bfe1120156c7e1ca2e6c4848e552fbaed58c98999de0acdc35f2162b1a7509e",
  },
};

// 解构导出所有属性，供其他模块直接导入
export const { clientId, clientSecret, tenantId, tokenHeader, tokenType, key, loginExpired, isRefreshToken, accessTokenKey, refreshTokenKey, fistPage, oauth2 } = website;

// 同时导出默认对象
export default website;
