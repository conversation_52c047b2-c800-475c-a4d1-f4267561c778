<template>
  <div class="flex flex-col gap-3.5">
    <div class="px-3" v-for="item in 5" :key="item">
      <div class="flex items-center justify-between">
        <p class="flex items-center gap-2.5">
          <span
            class="text-sm text-[#444] font-pingfang">蒲娜琴</span>
          <span
            class="text-xs text-[#909090] font-pingfang" v-if="options.zymc">2022通讯工程</span>
        </p>
        <p class="flex items-center gap-2.5">
          <span
            class="text-xs text-[#909090] font-pingfang" v-if="options.gkl">挂科率</span>
          <span class="text-[30px] text-[#FF4C00]">80%</span>
        </p>
      </div>
      <div
        class="h-[18px] bg-[#FBF9F7] rounded-[18px] mt-[10px]">
        <p
          class="h-full bg-linear-to-r from-[#FFE4BB] to-[#FF984C] rounded-[18px] transition-all"
          :style="{ width: `${progress || 100}%` }">
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
defineOptions({
  name: 'ProgressItem',
})
const props = defineProps({
  options: {
    type: Object,
    default: () => ({
      name: 'name',
      zymc: 'zymc',
      gkl: 'gkl',
      progress: 'progress',
    }),
  },
  data: {
    type: Array,
    default: () => [],
  },
})

const progress = ref(10)
</script>
