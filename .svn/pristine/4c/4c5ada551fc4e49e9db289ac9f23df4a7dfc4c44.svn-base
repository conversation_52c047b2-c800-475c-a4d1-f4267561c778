import { createApp, vaporInteropPlugin } from 'vue'
import * as directives from '@/directives'
import { setupStore } from '@/store'

import setupUtils from '@/utils'
import App from './App.vue'
// 全局注册@iconify/vue图标库
import {
  FontIcon,
  IconifyIconOffline,
  IconifyIconOnline,
} from './components/ReIcon/index'

import router from './router'
import './assets/styles/global.scss'
import './assets/styles/tailwind.css'
import './assets/styles/vant.scss'
import './assets/styles/vant-dark.scss'
/* --------------------------------
Vant 中有个别组件是以函数的形式提供的，
包括 Toast，Dialog，Notify 和 ImagePreview 组件。
在使用函数组件时，unplugin-vue-components
无法自动引入对应的样式，因此需要手动引入样式。
------------------------------------- */
import 'vant/es/toast/style'
import 'vant/es/dialog/style'
import 'vant/es/notify/style'
import 'vant/es/image-preview/style'
import 'vant/es/nav-bar/style'

const app = createApp(App)
app.use(vaporInteropPlugin)
Object.keys(directives).forEach((key) => {
  app.directive(key, directives[key])
})
app.component('IconifyIconOffline', IconifyIconOffline)
app.component('IconifyIconOnline', IconifyIconOnline)
app.component('FontIcon', FontIcon)

app.use(router)
app.use(setupUtils)
setupStore(app)
app.mount('#app')
