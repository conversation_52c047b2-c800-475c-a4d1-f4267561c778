import CryptoJS from 'crypto-js'

/**
 * 加密工具类
 * 提供 AES 和 DES 加密解密功能，与后端 Java 实现保持兼容
 *
 * @class crypto
 */
class crypto {
  /**
   * Token 加密密钥
   * 使用 @org.springblade.test.CryptoKeyGenerator 获取，需和后端配置保持一致
   * @type {string}
   * @static
   */
  static cryptoKey = '7wP4LXk3Z4ei3iXNAsYuTE0yJ5TqD9MG';
  /**
   * 报文加密key 使用@org.springblade.test.CryptoKeyGenerator获取,需和后端配置保持一致
   * @type {string}
   */
  static aesKey = 'qtrMh94rELyj7WoMxJe2Tuytoxe49FTI';
  /**
   * 报文加密key 使用@org.springblade.test.CryptoKeyGenerator获取,需和后端配置保持一致
   * @type {string}
   */
  static desKey = '7HMtIOznABl1j9CO';

  /**
   * 默认 AES 加密方法
   * 使用预设的 AES 密钥进行加密
   *
   * @param {string} data - 要加密的数据
   * @returns {string} 加密后的 Base64 字符串
   * @static
   */
  static encrypt(data) {
    return this.encryptAES(data, this.aesKey)
  }

  /**
   * 默认 AES 解密方法
   * 使用预设的 AES 密钥进行解密
   *
   * @param {string} data - 要解密的 Base64 字符串
   * @returns {string} 解密后的原始数据
   * @static
   */
  static decrypt(data) {
    return this.decryptAES(data, this.aesKey)
  }

  /**
   * AES 加密方法
   * 与 Java AesUtil.encryptToBase64(text, aesKey) 兼容
   *
   * @param {string} data - 要加密的数据
   * @param {string} key - 加密密钥
   * @returns {string} 加密后的 Base64 字符串
   * @static
   */
  static encryptAES(data, key) {
    const dataBytes = CryptoJS.enc.Utf8.parse(data)
    const keyBytes = CryptoJS.enc.Utf8.parse(key)
    const encrypted = CryptoJS.AES.encrypt(dataBytes, keyBytes, {
      iv: keyBytes,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    })
    return CryptoJS.enc.Base64.stringify(encrypted.ciphertext)
  }

  /**
   * AES 解密方法
   * 与 Java AesUtil.decryptFormBase64ToString(encrypt, aesKey) 兼容
   *
   * @param {string} data - 要解密的 Base64 字符串
   * @param {string} key - 解密密钥
   * @returns {string} 解密后的原始数据
   * @static
   */
  static decryptAES(data, key) {
    const keyBytes = CryptoJS.enc.Utf8.parse(key)
    const decrypted = CryptoJS.AES.decrypt(data, keyBytes, {
      iv: keyBytes,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    })
    return CryptoJS.enc.Utf8.stringify(decrypted)
  }

  /**
   * DES 加密方法
   * 与 Java DesUtil.encryptToBase64(text, desKey) 兼容
   *
   * @param {string} data - 要加密的数据
   * @param {string} key - 加密密钥
   * @returns {string} 加密后的字符串
   * @static
   */
  static encryptDES(data, key) {
    const keyHex = CryptoJS.enc.Utf8.parse(key)
    const encrypted = CryptoJS.DES.encrypt(data, keyHex, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    })
    return encrypted.toString()
  }

  /**
   * DES 解密方法
   * 与 Java DesUtil.decryptFormBase64(encryptBase64, desKey) 兼容
   *
   * @param {string} data - 要解密的 Base64 字符串
   * @param {string} key - 解密密钥
   * @returns {string} 解密后的原始数据
   * @static
   */
  static decryptDES(data, key) {
    const keyHex = CryptoJS.enc.Utf8.parse(key)
    const decrypted = CryptoJS.DES.decrypt(
      {
        ciphertext: CryptoJS.enc.Base64.parse(data),
      },
      keyHex,
      {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
      },
    )
    return decrypted.toString(CryptoJS.enc.Utf8)
  }
}

// 导出命名导出和默认导出
export { crypto }
export default crypto
