import http from "@cm/alova";

/*
 * @description 获取班级待办事项
 * @param {Object} params 请求参数
 * @returns {Promise<any>} 返回班级待办事项
 */
export function getClassTodoPageAPI(params) {
  return http.get("/blade-yjxxcj/yjxxCj/classTodoPage", { params });
}

/*
 * @description 获取待办事项
 * @param {Object} params 请求参数
 * @returns {Promise<any>} 返回待办事项
 */
export function getTodoPage(params) {
  return http.get("/blade-yjxxcj/yjxxCj/todoPage", { params });
}
/*
 * @description 获取待办事项数量
 * @param {Object} params 请求参数
 * @returns {Promise<any>} 返回待办事项数量
 */
export function getTodoPageCount(params) {
  return http.useRequest(() => http.get("/blade-yjxxcj/yjxxCj/todoPageCount", { params }), { immediate: true });
}
