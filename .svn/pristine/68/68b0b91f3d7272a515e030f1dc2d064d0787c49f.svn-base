<template>
  <div class="alarm-student h-full w-full flex flex-col">
    <div class="bg-white">
      <p class="bg-[#FDE9EB] p-2.5 text-[#C13C30] text-[24px] indent-2.5 font-normal font-pingfang">
        【温馨提示】根据您的考试成绩，教务系统考核您现在未达到毕业条件，请持续关注您的学业信息。（以下预警信息基于教务系统产生，最终解释权在教务处。如有疑问，请您登录教务系统于对应的查询界面进行核实，所有数据以教务系统为准）。
      </p>
      <TabsItem :options="tabsOptions" :businessActive="businessActive" :countData="warnTypeCount" @change="onTabBusinessClick" />
    </div>
    <div class="flex-1 min-h-0 px-[26px] overflow-y-auto my-3.75">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
        <div class="flex flex-col gap-y-[30px]">
          <ListItem v-for="item in list" :key="item" :options="listOptionsKey" :data="item" :btnName="item.clzt === 0 ? '处理' : '查看处理'" @btnClick="onBtnClick(item)">
            <template #title>
              <span>预警学生：{{ item.xsxm }}</span>
            </template>
          </ListItem>
        </div>
      </van-list>
    </div>
  </div>
</template>

<script setup>
import TabsItem from "@/views/todo/components/tabs-item.vue";
import ListItem from "@/views/todo/components/list-item.vue";
import { getAlarmAppStudentListAPI, getAlarmWarnTypeCountAPI } from "@/api/alarm";
defineOptions({
  name: "AlarmStudent",
  title: "预警信息-学生",
});
definePage({
  name: "AlarmStudent",
  meta: {
    layout: "index",
    title: "预警信息",
    navBar: true,
    isAuth: false,
  },
});
// 业务类型选择
const businessActive = ref("1");
const listOptionsKey = computed(() => {
  return Reflect.get(listOptions.value, tabsOptions.value.find((item) => item.key === businessActive.value)?.listOptionsKey);
});
// 业务类型选择选项
const tabsOptions = ref([
  {
    label: "必修",
    key: "1",
    tabsCountKey: "cjyjsl",
    listOptionsKey: "bx",
  },
  {
    label: "选修",
    key: "4",
    tabsCountKey: "xxyjsl",
    listOptionsKey: "xx",
  },
  {
    label: "实习",
    key: "2",
    tabsCountKey: "dgsxyjsl",
    listOptionsKey: "bx",
  },
  {
    label: "毕设",
    key: "3",
    tabsCountKey: "bysjyjsl",
    listOptionsKey: "bx",
  },
]);

// 列表选项
const listOptions = ref({
  bx: [
    {
      label: "预警课程",
      value: "kcmc",
    },
    {
      label: "开课学年/学期",
      value: "xnxq",
    },
    {
      label: "实得成绩",
      value: "sdcj",
    },
    {
      label: "成绩类型",
      value: "cjlxmc",
    },
  ],
  xx: [
    {
      label: "已修课程",
      value: "xxhgs",
      format: (row) => {
        return `${row.xxhgs || 0}门`;
      },
    },
    {
      label: "待修课程",
      value: "xxqks",
      format: (row) => {
        return `${row.xxqks || 0}门`;
      },
    },
  ],
});

const list = ref([]);
const loading = ref(false);
const finished = ref(false);
const pageCurrent = ref(1);
const onLoad = () => {
  getAlarmAppStudentListAPI({ yjlx: businessActive.value, current: pageCurrent.value, size: 10 }).then((res) => {
    if (pageCurrent.value === 1) {
      list.value = res.records;
    } else {
      list.value.push(...res.records);
    }
    loading.value = false;
    if (res.total <= list.value.length) {
      finished.value = true;
    } else {
      pageCurrent.value++;
    }
  });
};

// 预警类型统计
const warnTypeCount = ref({});
const getWarnTypeCount = () => {
  getAlarmWarnTypeCountAPI().then((res) => {
    warnTypeCount.value = res;
  });
};
getWarnTypeCount();
// 刷新
const onRefresh = () => {
  // 清空列表数据
  finished.value = false;
  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  pageCurrent.value = 1;
  list.value.length = 0;
  onLoad();
};

const onTabBusinessClick = (key) => {
  businessActive.value = key;
  onRefresh();
};
</script>

<style lang="scss" scoped>
.alarm-student {
  background: #f0f2f9;
  font-size: 14px;
  .business-tabs {
    &::after {
      content: "";
      position: absolute;
      left: var(--indicator-left, 12.5%);
      bottom: 0;
      width: 24px;
      height: 4px;
      background: #0e5fff;
      transform: translateX(-50%);
      transition: all 0.3s ease-in-out;
    }
  }
}
</style>
