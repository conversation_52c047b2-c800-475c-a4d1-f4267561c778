import { defineStore } from 'pinia'
import { ref } from 'vue'
import { store } from '@/store'

const useAppStore = defineStore('app', () => {
  const keepAlive = ref([])
  // 主题相关状态
  const theme = ref('auto') // 'auto'表示自动，'light'表示亮色模式，'dark'表示暗色模式
  const setKeepAlive = (name) => {
    if (!keepAlive.value.includes(name)) {
      keepAlive.value.push(name)
    }
  }
  const removeKeepAlive = (name) => {
    keepAlive.value = keepAlive.value.filter(item => item !== name)
  }
  const clearKeepAlive = () => {
    keepAlive.value = []
  }

  // 设置主题模式
  const setTheme = (newTheme) => {
    theme.value = newTheme
  }

  return {
    keepAlive,
    theme,
    setKeepAlive,
    removeKeepAlive,
    clearKeepAlive,
    setTheme,
  }
}, {
  persist: {
    key: 'app-theme',
    paths: ['theme'],
  },
})

export default () => useAppStore(store)
