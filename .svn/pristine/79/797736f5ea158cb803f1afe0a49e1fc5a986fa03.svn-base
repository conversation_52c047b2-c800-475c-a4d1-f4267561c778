/**
 * @cm/alova HTTP请求Loading状态管理器
 *
 * 重构版本 - 简化架构设计，优化性能表现
 * 使用直接Map操作 + 状态计数器跟踪并发HTTP请求
 * 集成Vant UI的Toast.loading组件，提供统一的loading状态管理
 *
 * <AUTHOR>
 * @version 3.0.0
 */

import { allowMultipleToast, showLoadingToast, showToast } from "vant";

/**
 * Loading状态枚举
 */
const LOADING_STATES = {
  IDLE: "idle", // 空闲状态，无请求进行
  LOADING: "loading", // 正在加载，显示loading
  WAITING_MIN_TIME: "waiting_min_time", // 等待最小显示时间
  HIDING: "hiding", // 正在隐藏loading
};

/**
 * Loading管理器配置
 */
const LOADING_CONFIG = {
  enabled: true,
  skipLoadingHeader: "X-Skip-Loading",
  minLoadingTime: 300,
  message: "加载中...",
  errorToastDebounce: 500, // 错误Toast防抖时间（可配置）
  toastConfig: {
    forbidClick: true,
    loadingType: "spinner",
    duration: 0,
    overlay: true,
    overlayStyle: { background: "rgba(0, 0, 0, 0)" },
  },
};

/**
 * @cm/alova HTTP请求Loading状态管理器
 *
 * 核心功能：
 * - 使用直接Map操作跟踪并发HTTP请求
 * - 自动显示/隐藏Vant UI Toast.loading
 * - 支持最小显示时间防止闪烁
 * - 支持跳过loading的请求配置
 * - 优化的错误Toast队列管理
 *
 * @class HttpLoadingManager
 */
class HttpLoadingManager {
  // === 核心状态管理 ===
  /** @type {Map<string, object>} 活跃请求Map */
  #activeRequests = new Map();

  /** @type {string} 当前loading状态 */
  #currentState = LOADING_STATES.IDLE;

  /** @type {number} 请求ID计数器 */
  #requestIdCounter = 0;

  // === UI相关状态 ===
  /** @type {object|null} 当前显示的loading Toast实例 */
  #currentLoadingToast = null;

  /** @type {number|null} 最小显示时间定时器ID */
  #minTimeTimer = null;

  /** @type {number} loading开始显示的时间戳 */
  #loadingStartTime = 0;

  // === 错误处理相关 ===
  /** @type {Set<string>} 错误Toast消息队列（使用Set去重） */
  #errorToastQueue = new Set();

  /** @type {boolean} 是否正在处理错误Toast队列 */
  #isProcessingErrorQueue = false;

  /** @type {number|null} 错误处理防抖定时器 */
  #errorDebounceTimer = null;

  // === 重试管理相关 (已移除) ===

  /**
   * 构造函数 - 初始化管理器
   */
  constructor() {
    allowMultipleToast();
  }

  // === 核心状态管理方法 ===

  /**
   * 生成唯一的请求ID - 简化版本
   * 使用时间戳+递增计数器确保唯一性
   *
   * @param {object} method - alova Method实例
   * @returns {string} 唯一请求ID
   */
  #generateRequestId(method) {
    this.#requestIdCounter = (this.#requestIdCounter + 1) % 10000;
    const methodInfo = `${method.type}_${method.url}`.replace(/[^a-z0-9]/gi, "_").substring(0, 20);
    return `${methodInfo}_${Date.now()}_${this.#requestIdCounter}`;
  }

  /**
   * 检查请求是否应该跳过loading显示
   *
   * @param {object} method - alova Method实例
   * @returns {boolean} 是否跳过loading
   */
  #shouldSkipLoading(method) {
    return !LOADING_CONFIG.enabled || method.config?.headers?.[LOADING_CONFIG.skipLoadingHeader] || method.meta?.skipLoading === true;
  }

  /**
   * 添加请求到活跃请求池
   *
   * @param {string} requestId - 请求ID
   * @param {object} requestInfo - 请求信息
   * @private
   */
  #addActiveRequest(requestId, requestInfo) {
    this.#activeRequests.set(requestId, requestInfo);

    // 如果是第一个请求，开始显示loading
    if (this.#activeRequests.size === 1 && this.#currentState === LOADING_STATES.IDLE) {
      this.#showLoading();
    }
  }

  /**
   * 从活跃请求池移除请求
   *
   * @param {string} requestId - 请求ID
   * @private
   */
  #removeActiveRequest(requestId) {
    const wasRemoved = this.#activeRequests.delete(requestId);

    // 如果移除成功且没有活跃请求了，开始隐藏loading
    if (wasRemoved && this.#activeRequests.size === 0) {
      this.#hideLoading();
    }
  }

  // === Loading UI管理方法 ===

  /**
   * 显示loading Toast
   *
   * @private
   */
  #showLoading() {
    if (this.#currentState !== LOADING_STATES.IDLE) {
      return;
    }
    try {
      this.#currentState = LOADING_STATES.LOADING;
      this.#loadingStartTime = Date.now();
      this.#currentLoadingToast = showLoadingToast({
        message: LOADING_CONFIG.message,
        ...LOADING_CONFIG.toastConfig,
      });
    } catch (error) {
      console.error("[HttpLoadingManager] 显示loading失败:", error);
      this.#currentState = LOADING_STATES.IDLE;
    }
  }

  /**
   * 隐藏loading Toast - 考虑最小显示时间
   *
   * @private
   */
  #hideLoading() {
    if (this.#currentState === LOADING_STATES.IDLE || this.#currentState === LOADING_STATES.HIDING) {
      return;
    }

    const elapsedTime = Date.now() - this.#loadingStartTime;
    const remainingTime = LOADING_CONFIG.minLoadingTime - elapsedTime;

    if (remainingTime <= 0) {
      this.#doHideLoading();
    } else {
      this.#currentState = LOADING_STATES.WAITING_MIN_TIME;
      this.#minTimeTimer = setTimeout(() => this.#doHideLoading(), remainingTime);
    }
  }

  /**
   * 执行loading隐藏操作
   *
   * @private
   */
  #doHideLoading() {
    if (this.#currentState === LOADING_STATES.IDLE) {
      return;
    }

    try {
      this.#currentState = LOADING_STATES.HIDING;

      if (this.#currentLoadingToast) {
        this.#currentLoadingToast.close();
        this.#currentLoadingToast = null;
      }

      if (this.#minTimeTimer) {
        clearTimeout(this.#minTimeTimer);
        this.#minTimeTimer = null;
      }

      this.#currentState = LOADING_STATES.IDLE;

      // Loading隐藏后处理错误Toast队列
      this.#processErrorToastQueue();
    } catch (error) {
      console.error("[HttpLoadingManager] 隐藏loading失败:", error);
      this.#currentState = LOADING_STATES.IDLE;
    }
  }

  // === 错误处理方法 ===

  /**
   * 处理错误Toast队列 - 优化版本
   * 使用防抖机制避免重复错误消息，Set数据结构自动去重
   *
   * @private
   */
  #processErrorToastQueue() {
    // 如果没有错误消息或正在显示loading，则不处理
    if (this.#errorToastQueue.size === 0 || this.#currentState !== LOADING_STATES.IDLE) {
      return;
    }

    // 清除之前的防抖定时器
    if (this.#errorDebounceTimer) {
      clearTimeout(this.#errorDebounceTimer);
    }

    // 使用配置的防抖时间
    this.#errorDebounceTimer = setTimeout(() => {
      if (this.#errorToastQueue.size === 0 || this.#isProcessingErrorQueue) {
        return;
      }

      this.#isProcessingErrorQueue = true;

      // 获取第一个错误消息（Set的迭代是有序的）
      const firstMessage = this.#errorToastQueue.values().next().value;
      this.#errorToastQueue.delete(firstMessage);

      showToast({
        message: firstMessage,
        duration: 3000,
        onClose: () => {
          this.#isProcessingErrorQueue = false;
          // 继续处理剩余的错误消息
          this.#processErrorToastQueue();
        },
      });
    }, LOADING_CONFIG.errorToastDebounce);
  }

  // === 公共API方法 ===

  /**
   * 开始跟踪HTTP请求
   * 在请求发起时调用
   *
   * @param {object} method - alova Method实例
   * @returns {string|null} 请求ID，如果跳过loading则返回null
   */
  startRequest(method) {
    if (this.#shouldSkipLoading(method)) {
      return null;
    }

    const requestId = this.#generateRequestId(method);
    const requestInfo = {
      id: requestId,
      url: method.url,
      type: method.type,
      startTime: Date.now(),
    };

    this.#addActiveRequest(requestId, requestInfo);
    return requestId;
  }

  /**
   * 结束跟踪HTTP请求
   * 在请求完成（成功或失败）时调用
   *
   * @param {string|null} requestId - 请求ID
   */
  endRequest(requestId) {
    if (requestId && this.#activeRequests.has(requestId)) {
      this.#removeActiveRequest(requestId);
    }
  }

  /**
   * 添加错误Toast到队列
   * 使用Set自动去重相同的错误消息
   *
   * @param {string} message - 错误消息
   * @returns {boolean} 是否成功添加（false表示消息已存在）
   */
  addErrorToast(message) {
    const wasNew = !this.#errorToastQueue.has(message);
    this.#errorToastQueue.add(message);

    // 如果当前没有loading显示，立即尝试处理错误队列
    if (this.#currentState === LOADING_STATES.IDLE) {
      this.#processErrorToastQueue();
    }

    return wasNew;
  }

  /**
   * 获取当前活跃请求数量
   *
   * @returns {number} 活跃请求数量
   */
  getActiveRequestCount() {
    return this.#activeRequests.size;
  }

  /**
   * 获取所有活跃请求信息
   *
   * @returns {Array<object>} 活跃请求信息数组
   */
  getActiveRequests() {
    return Array.from(this.#activeRequests.values());
  }

  /**
   * 强制清理所有请求并隐藏loading
   * 用于错误恢复或页面切换时的状态重置
   */
  reset() {
    // 清理所有状态
    this.#activeRequests.clear();
    this.#errorToastQueue.clear();
    this.#isProcessingErrorQueue = false;

    // 清理重试Toast (已移除)

    // 清理定时器
    if (this.#minTimeTimer) {
      clearTimeout(this.#minTimeTimer);
      this.#minTimeTimer = null;
    }

    if (this.#errorDebounceTimer) {
      clearTimeout(this.#errorDebounceTimer);
      this.#errorDebounceTimer = null;
    }

    // 强制隐藏loading
    this.#currentState = LOADING_STATES.LOADING; // 设置为LOADING状态以允许隐藏
    this.#doHideLoading();
  }

  /**
   * 更新loading配置
   *
   * @param {object} config - 新的loading配置
   */
  configure(config = {}) {
    Object.assign(LOADING_CONFIG, config);
  }

  /**
   * 获取错误Toast队列状态
   *
   * @returns {object} 错误Toast队列状态信息
   */
  getErrorToastStatus() {
    return {
      queueSize: this.#errorToastQueue.size,
      isProcessing: this.#isProcessingErrorQueue,
      messages: Array.from(this.#errorToastQueue),
    };
  }

  // === 重试UI管理方法 (已移除) ===

  /**
   * 获取当前loading状态信息
   *
   * @returns {object} loading状态信息
   */
  getStatus() {
    return {
      state: this.#currentState,
      isLoading: this.#currentState === LOADING_STATES.LOADING || this.#currentState === LOADING_STATES.WAITING_MIN_TIME,
      activeRequestCount: this.#activeRequests.size,
      loadingStartTime: this.#loadingStartTime,
      config: { ...LOADING_CONFIG },
      errorToastQueue: this.getErrorToastStatus(),
      // retryToasts: (已移除)
    };
  }
}

// 创建全局HTTP loading管理器实例
const httpLoadingManager = new HttpLoadingManager();

export default httpLoadingManager;
