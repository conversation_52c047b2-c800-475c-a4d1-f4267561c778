# =============================================================================
# Docker 构建忽略文件
# 
# 这个文件告诉 Docker 在构建镜像时忽略哪些文件和目录
# 类似于 .gitignore，但专门用于 Docker 构建过程
# 
# 忽略这些文件的好处：
# 1. 减少构建上下文大小，加快构建速度
# 2. 避免敏感信息泄露到镜像中
# 3. 保持镜像内容的整洁
# =============================================================================

# -----------------------------------------------------------------------------
# 版本控制系统文件
# -----------------------------------------------------------------------------
.git
.gitignore
.gitattributes

# -----------------------------------------------------------------------------
# 依赖目录
# -----------------------------------------------------------------------------
# Node.js 依赖目录，会在容器内重新安装
node_modules
**/node_modules

# -----------------------------------------------------------------------------
# 构建产物和缓存
# -----------------------------------------------------------------------------
# 构建输出目录，会在容器内重新生成
dist
out

# 各种缓存目录
.cache
.vite
.turbo
.eslintcache
.parcel-cache

# -----------------------------------------------------------------------------
# 开发工具配置
# -----------------------------------------------------------------------------
# IDE 和编辑器配置
.vscode
.idea
*.swp
*.swo
*~

# OS 生成的文件
.DS_Store
Thumbs.db
desktop.ini

# -----------------------------------------------------------------------------
# 日志文件
# -----------------------------------------------------------------------------
# 各种日志文件
*.log
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# -----------------------------------------------------------------------------
# 运行时文件
# -----------------------------------------------------------------------------
# 进程 ID 文件
*.pid
*.seed
*.pid.lock

# 覆盖率报告
coverage
*.lcov

# -----------------------------------------------------------------------------
# 测试相关
# -----------------------------------------------------------------------------
# 测试覆盖率
.nyc_output


# 但是保留示例环境文件
!.env.example

# -----------------------------------------------------------------------------
# Docker 相关文件
# -----------------------------------------------------------------------------
# Docker 相关文件（避免递归复制）
Dockerfile*
docker-compose*
.dockerignore

# -----------------------------------------------------------------------------
# 文档和说明
# -----------------------------------------------------------------------------
# 开发文档（生产环境不需要）
README.md
CHANGELOG.md
LICENSE
*.md

# 但是保留可能被应用使用的 markdown 文件
!src/**/*.md

# -----------------------------------------------------------------------------
# 临时文件
# -----------------------------------------------------------------------------
# 临时目录
tmp
temp

# 系统临时文件
*.tmp
*.temp

# -----------------------------------------------------------------------------
# 其他工具配置
# -----------------------------------------------------------------------------
# Webpack 分析文件
stats.json

# 包分析文件
bundle-analyzer-report.html

# TypeScript 声明文件缓存
*.tsbuildinfo

# -----------------------------------------------------------------------------
# 说明：
# 
# 1. 以 # 开头的行是注释
# 2. 每行一个模式，支持通配符
# 3. ! 开头表示不忽略（例外规则）
# 4. ** 表示匹配任意层级的目录
# 5. * 表示匹配任意字符（除了路径分隔符）
# 
# 建议：
# - 定期检查和更新这个文件
# - 根据项目实际情况调整忽略规则
# - 确保不会忽略构建所需的重要文件
# ============================================================================= 