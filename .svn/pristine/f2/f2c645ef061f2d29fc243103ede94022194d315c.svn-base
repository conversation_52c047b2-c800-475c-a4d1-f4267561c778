import storage from "@cm/utils/storage";
import { create<PERSON><PERSON><PERSON>, Method } from "alova";
import adapterFetch from "alova/fetch";
import VueHook from "alova/vue";
import { Base64 } from "js-base64";
import { clientId, clientSecret, tenantId } from "@/config/website.js";
import { onAuthRequired, onResponseRefreshToken } from "./auth";
import errorHandlerRegistry from "./core/error-handler-registry.js";
import HttpCode from "./ErrorCode";
import httpLoadingManager from "./loading-manager.js";
import logger from "./utils/logger.js";

/**
 * HTTP请求配置
 */
const HTTP_CONFIG = {
  // 基础配置
  baseURL: import.meta.env.VITE_APP_BASE_URL,
  timeout: 3000,

  // 默认请求头
  defaultHeaders: {
    Accept: "application/json, text/plain, */*",
    "X-Requested-With": "XMLHttpRequest",
    "Blade-Requested-With": "BladeHttpRequest",
    Authorization: `Basic ${Base64.encode(`${clientId}:${clientSecret}`)}`,
    "tenant-Id": tenantId,
  },

  // 错误处理配置
  errorHandling: {
    showToastOnError: true,
    logErrors: false,
  },
  cacheFor: null,
};

/**
 * 错误处理配置
 * 与新的错误处理系统集成
 */
const ERROR_CONFIG = {
  // 是否自动显示Toast（由错误处理器控制）
  autoShowToast: HTTP_CONFIG.errorHandling.showToastOnError,
  // 是否记录详细日志
  detailedLogging: HTTP_CONFIG.errorHandling.logErrors,
};

/**
 * 创建错误上下文对象 - 优化版本
 *
 * 使用对象字面量和计算属性提升性能
 * 减少不必要的属性访问和条件判断
 *
 * @param {Error} error - 错误对象
 * @param {string} customMessage - 自定义错误消息
 * @param {string} errorType - 错误类型
 * @param {object} method - alova method 实例
 * @returns {object} 优化的错误上下文对象
 */
function createErrorContext(error, customMessage, errorType, method) {
  const now = new Date();

  return {
    type: errorType,
    message: customMessage || error?.message || "未知错误",
    url: method?.url || "unknown",
    method: method?.type || "unknown",
    timestamp: now.toISOString(),
    status: error?.status || null,
    // 新增调试信息
    stack: error?.stack || null,
    requestId: method?.meta?.httpLoadingRequestId || null,
    duration: method?.meta?.startTime ? now.getTime() - method.meta.startTime : null,
  };
}

/**
 * 统一错误处理函数 - 策略模式重构版本
 *
 * 使用新的错误处理器注册表系统
 * 支持自动重试、详细日志记录和智能错误分类
 *
 * @param {Error} error - 错误对象
 * @param {string} customMessage - 自定义错误消息
 * @param {object} method - alova method 实例
 * @returns {object} 错误处理结果
 */
function handleRequestError(error, customMessage = "", method = null) {
  // 创建错误上下文
  const errorContext = createErrorContext(error, customMessage, "auto", method);

  // 使用错误处理器注册表处理错误
  const result = errorHandlerRegistry.handle(error, customMessage, errorContext);

  // 记录详细错误日志
  if (ERROR_CONFIG.detailedLogging) {
    logger.error("HTTP请求错误处理完成:", {
      handlerType: result.metadata?.handlerType,
      message: result.message,
      severity: result.severity,
      errorContext,
      originalError: {
        name: error?.name,
        message: error?.message,
        stack: error?.stack,
      },
    });
  }

  return result;
}

// 移除了 classifyError 函数
// 新的错误处理器注册表会自动进行错误分类和处理

/**
 * @cm/alova Methods 基础类 - HTTP 请求方法封装
 *
 * 提供基础的 HTTP 请求方法和 alova 实例管理
 * 集成认证、错误处理、缓存等企业级功能
 *
 * @class Methods
 * <AUTHOR>
 * @version 2.0.0
 */
class Methods {
  /** @type {import('alova').Alova} alova 实例 */
  #alovaInstance = null;
  /**
   * 构造函数 - 初始化 alova 实例
   *
   * 使用 ES6+ 语法优化配置对象，提供完整的企业级功能
   */
  constructor() {
    this.#alovaInstance = createAlova({
      // 基础配置 - 使用解构赋值优化
      baseURL: HTTP_CONFIG.baseURL,
      statesHook: VueHook,
      requestAdapter: adapterFetch(),
      timeout: HTTP_CONFIG.timeout,
      cacheFor: HTTP_CONFIG.cacheFor,
      l2Cache: storage,
      // 请求共享配置 - alova 自动实现请求去重
      shareRequest: true,

      // 请求拦截器 - 使用箭头函数和展开语法优化
      beforeRequest: onAuthRequired((method) => {
        // 使用展开语法合并请求头，避免修改原对象
        method.config.headers = { ...HTTP_CONFIG.defaultHeaders, ...method.config.headers };

        // 使用空值合并运算符优化元数据初始化
        method.meta ??= {};
        if (!Reflect.has(method.config, "responseData")) {
          method.meta.responseData = true;
        }
        method.meta.startTime = Date.now();
        // 集成HTTP loading管理器 - 开始跟踪请求
        if (!method.meta.httpLoadingRequestId) {
          const httpLoadingRequestId = httpLoadingManager.startRequest(method);
          method.meta.httpLoadingRequestId = httpLoadingRequestId;
        }
      }),

      // 响应拦截器 - 优化错误处理逻辑
      responded: onResponseRefreshToken({
        onSuccess: async (response, method) => {
          // 结束HTTP loading跟踪 - 成功响应
          httpLoadingManager.endRequest(method.meta?.httpLoadingRequestId);
          if (response.status !== 200) {
            const error = new Error(HttpCode[response.status] || HttpCode.default);
            error.status = response.status;
            handleRequestError(error, "", method);
            return Promise.reject(error);
          }

          const json = await response.json();
          // 使用解构赋值和逻辑运算符优化业务错误检查
          const { code, error_code, message, error_description } = json;
          const hasBusinessError = (code && code !== 200) || error_code === 2001;
          if (hasBusinessError) {
            const errorMessage = message || error_description || HttpCode.default;
            const businessError = new Error(errorMessage);
            businessError.code = code || error_code;
            handleRequestError(businessError, errorMessage, method);
            return Promise.reject(errorMessage);
          }
          if (method.meta.responseData) {
            return json?.data || json;
          }
          // 使用空值合并运算符优化数据返回
          return json;
        },

        onError: (error, method) => {
          // 结束HTTP loading跟踪 - 错误响应
          httpLoadingManager.endRequest(method.meta?.httpLoadingRequestId);
          handleRequestError(error, "", method);
          return Promise.reject(error);
        },

        onComplete: (method) => {
          // 确保HTTP loading跟踪结束 - 完成回调（兜底处理）
          httpLoadingManager.endRequest(method.meta?.httpLoadingRequestId);
          return method;
        },
      }),
    });
  }

  /**
   * 创建 alova Method 实例
   *
   * @param {string} method - HTTP 方法
   * @param {import('alova').Alova} alovaInstance - alova 实例
   * @param {string} url - 请求 URL
   * @param {object} [config] - 请求配置
   * @param {any} [data] - 请求数据
   *
   */

  request(config = {}) {
    return this.#alovaInstance.Request(config);
  }

  /**
   * GET 请求方法
   *
   * @param {string} url - 请求 URL
   * @param {object} [config] - 请求配置，params 参数应通过 config.params 传递
   * @returns {import('alova').Method} alova Method 实例
   */
  get(url, config = {}) {
    return new Method("get", this.#alovaInstance, url, { ...config });
  }

  /**
   * POST 请求方法
   *
   * @param {string} url - 请求 URL
   * @param {any} data - 请求数据
   * @param {object} [config] - 请求配置
   * @returns {import('alova').Method} alova Method 实例
   */
  post(url, data, config = {}) {
    return new Method("post", this.#alovaInstance, url, config, data);
  }
}

// 导出类、默认实例和HTTP loading管理器
export { Methods };
