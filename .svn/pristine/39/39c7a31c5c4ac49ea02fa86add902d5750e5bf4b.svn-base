<template>
  <div class="bg-[#f0f2f9] h-full w-full flex flex-col">
    <div class="bg-white">
      <p class="bg-[#FFF8F0] p-2.5 text-[#EC8800] text-[24px] font-normal font-pingfang">【温馨提示】学生毕业的最终解释权归四川邮电职业技术学院所有</p>
      <SearchItem v-model:searchParams="searchParams" :options="searchOptions" />
    </div>
    <div class="flex-1 min-h-0 px-[26px] overflow-y-auto my-3.75">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
        <div class="flex flex-col gap-y-[30px]">
          <ListItem v-for="item in list" :key="item" :isBtn="item.clzt === 0" btnName="批量处理" :options="listOptions" :data="item" @btnClick="onBtnClick(item)" @click="onItemClick(item)">
            <template #title>
              <span>预警院系：{{ item.xymc }}</span>
            </template>
          </ListItem>
        </div>
      </van-list>
    </div>
  </div>
</template>

<script setup>
import ListItem from "@/views/todo/components/list-item.vue";
import SearchItem from "@/views/todo/components/search-item.vue";
import { getAlarmListAPI } from "@/api/alarm";
defineOptions({
  name: "LeaderCollegeMajor",
  title: "校领导-院系专业",
});
definePage({
  name: "LeaderCollegeMajor",
  meta: {
    layout: "index",
    title: "预警信息",
    navBar: true,
    isAuth: true,
  },
});

const searchParams = ref({
  nj: "",
  yxbh: "",
  zybh: "",
});
const searchOptions = ref([
  {
    key: "yx",
    label: "院系",
    value: "yxbh",
    type: "select",
    placeholder: "全部院系",
  },
  {
    key: "zy",
    label: "专业",
    value: "zybh",
    type: "select",
    placeholder: "全部专业",
  },
  {
    key: "nj", //获取选择数据源
    label: "年级",
    value: "nj",
    type: "select",
    placeholder: "全部年级",
  },
]);

const listOptions = ref([
  {
    label: "预警批次",
    value: "yjbh",
  },
  {
    label: "预警时间",
    value: "yjsj",
  },
  {
    label: "预警专业",
    value: "zymc",
  },
  {
    label: "预警年级",
    value: "nj",
  },
  {
    label: "预警人数",
    value: "yjrs",
    format: (row) => {
      return `${row.yjrs || 0}人`;
    },
  },
  {
    label: "预警课程数",
    value: "yjkcs",
    format: (row) => {
      return `${row.yjkcs || 0}门`;
    },
  },
]);
const list = ref([]);
const loading = ref(false);
const finished = ref(false);
const pageCurrent = ref(1);
const onLoad = () => {
  // 异步更新数据
  // setTimeout 仅做示例，真实场景中一般为 ajax 请求
  getAlarmListAPI({ ...searchParams.value, current: pageCurrent.value, size: 10 }).then((res) => {
    if (pageCurrent.value === 1) {
      list.value = res.records;
    } else {
      list.value.push(...res.records);
    }
    // 加载状态结束
    loading.value = false;
    if (res.total <= list.value.length) {
      finished.value = true;
    } else {
      pageCurrent.value++;
    }
  });
};
const onRefresh = () => {
  // 清空列表数据
  finished.value = false;
  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  pageCurrent.value = 1;
  list.value.length = 0;
  onLoad();
};
watch(
  searchParams,
  () => {
    onRefresh();
  },
  {
    deep: true,
  },
);

const router = useRouter();
const onBtnClick = (row) => {
  router.push({
    path: "/todo/batch",
    query: {
      nj: row.nj,
      yjbh: row.yjbh,
      yxbh: row.yxbh,
      zybh: row.zybh,
      sfxx: "0", // 是否选修 批量处理提交时需要字段
      type: "handle",
    },
  });
};

const onItemClick = (row) => {
  router.push({
    path: "/alarm/leader/class",
    query: {
      yjbh: row.yjbh,
      nj: row.nj,
      yxbh: row.yxbh,
      zybh: row.zybh,
    },
  });
};
</script>

<style lang="scss" scoped></style>
