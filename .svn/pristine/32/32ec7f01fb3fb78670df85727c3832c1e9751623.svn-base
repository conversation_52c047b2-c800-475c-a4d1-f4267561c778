/**
 * 高性能深拷贝工具模块
 * 支持循环引用处理和各种数据类型的深拷贝
 */

/**
 * 高性能深拷贝函数
 *
 * @param {*} value - 要深拷贝的值
 * @param {WeakMap} [cache] - 用于内部递归调用时缓存已处理对象
 * @returns {*} 返回深拷贝后的值
 */
function cloneDeep(value, cache = new WeakMap()) {
  // 处理基本类型、null和函数
  if (value === null || typeof value !== 'object' || typeof value === 'function') {
    return value
  }

  // 检查循环引用
  if (cache.has(value)) {
    return cache.get(value)
  }

  // 处理特殊内置对象
  // 日期对象
  if (value instanceof Date) {
    return new Date(value.getTime())
  }

  // 正则表达式
  if (value instanceof RegExp) {
    return new RegExp(value.source, value.flags)
  }

  // ArrayBuffer
  if (value instanceof ArrayBuffer) {
    const clone = value.slice(0)
    cache.set(value, clone)
    return clone
  }

  // 处理类型化数组
  if (
    value instanceof Int8Array
    || value instanceof Uint8Array
    || value instanceof Uint8ClampedArray
    || value instanceof Int16Array
    || value instanceof Uint16Array
    || value instanceof Int32Array
    || value instanceof Uint32Array
    || value instanceof Float32Array
    || value instanceof Float64Array
    || value instanceof BigInt64Array
    || value instanceof BigUint64Array
  ) {
    const clone = value.slice(0)
    cache.set(value, clone)
    return clone
  }

  // 处理Blob
  if (value instanceof Blob) {
    return new Blob([value], { type: value.type })
  }

  // 处理数组
  if (Array.isArray(value)) {
    const clone = []
    cache.set(value, clone)

    // 使用forEach性能更好
    value.forEach((item, index) => {
      clone[index] = cloneDeep(item, cache)
    })

    return clone
  }

  // 处理Map
  if (value instanceof Map) {
    const clone = new Map()
    cache.set(value, clone)

    value.forEach((val, key) => {
      // Map的键也可能是对象，需要深拷贝
      clone.set(cloneDeep(key, cache), cloneDeep(val, cache))
    })

    return clone
  }

  // 处理Set
  if (value instanceof Set) {
    const clone = new Set()
    cache.set(value, clone)

    value.forEach((item) => {
      clone.add(cloneDeep(item, cache))
    })

    return clone
  }

  // 处理普通对象
  try {
    // 保留原型链
    const clone = Object.create(Object.getPrototypeOf(value))
    cache.set(value, clone)

    // 获取所有属性，包括不可枚举属性和Symbol属性
    const allProps = [
      ...Object.getOwnPropertyNames(value),
      ...Object.getOwnPropertySymbols(value),
    ]

    // 复制所有属性
    allProps.forEach((prop) => {
      const descriptor = Object.getOwnPropertyDescriptor(value, prop)

      // 确保descriptor存在
      if (descriptor) {
        // 处理getter/setter
        if (descriptor.get || descriptor.set) {
          Object.defineProperty(clone, prop, descriptor)
        }
        else {
          // 递归复制属性值
          clone[prop] = cloneDeep(value[prop], cache)
        }
      }
    })

    return clone
  }
  catch (error) {
    void error // 明确忽略错误信息
    // 处理不可克隆的对象，如Error对象等
    // 简单地返回一个新的空对象，保留原始对象的属性
    const fallbackObj = {}

    for (const key in value) {
      if (Object.prototype.hasOwnProperty.call(value, key)) {
        fallbackObj[key] = cloneDeep(value[key], cache)
      }
    }

    return fallbackObj
  }
}

/**
 * 浅拷贝函数
 * @param {*} value - 要浅拷贝的值
 * @returns {*} 浅拷贝后的值
 */
function shallowClone(value) {
  if (value === null || typeof value !== 'object') {
    return value
  }

  if (Array.isArray(value)) {
    return [...value]
  }

  if (value instanceof Date) {
    return new Date(value.getTime())
  }

  if (value instanceof RegExp) {
    return new RegExp(value.source, value.flags)
  }

  if (value instanceof Map) {
    return new Map(value)
  }

  if (value instanceof Set) {
    return new Set(value)
  }

  // 普通对象
  return { ...value }
}

/**
 * 检查两个值是否深度相等
 * @param {*} a - 第一个值
 * @param {*} b - 第二个值
 * @param {WeakMap} [cache] - 用于循环引用检测的缓存
 * @returns {boolean} 是否深度相等
 */
function isDeepEqual(a, b, cache = new WeakMap()) {
  // 严格相等检查
  if (a === b) {
    return true
  }

  // 类型检查
  if (typeof a !== typeof b) {
    return false
  }

  // null 检查
  if (a === null || b === null) {
    return a === b
  }

  // 非对象类型
  if (typeof a !== 'object') {
    return a === b
  }

  // 循环引用检查
  if (cache.has(a)) {
    return cache.get(a) === b
  }
  cache.set(a, b)

  // 数组检查
  if (Array.isArray(a) !== Array.isArray(b)) {
    return false
  }

  if (Array.isArray(a)) {
    if (a.length !== b.length) {
      return false
    }
    for (let i = 0; i < a.length; i++) {
      if (!isDeepEqual(a[i], b[i], cache)) {
        return false
      }
    }
    return true
  }

  // 对象键检查
  const keysA = Object.keys(a)
  const keysB = Object.keys(b)

  if (keysA.length !== keysB.length) {
    return false
  }

  for (const key of keysA) {
    if (!keysB.includes(key)) {
      return false
    }
    if (!isDeepEqual(a[key], b[key], cache)) {
      return false
    }
  }

  return true
}

// 导出函数
export {
  cloneDeep,
  shallowClone,
  isDeepEqual,
}

// 默认导出主函数
export default cloneDeep
