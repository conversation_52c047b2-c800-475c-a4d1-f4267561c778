{"compilerOptions": {"target": "ESNext", "lib": ["DOM", "ESNext"], "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.json"], "exclude": ["node_modules", "dist"]}