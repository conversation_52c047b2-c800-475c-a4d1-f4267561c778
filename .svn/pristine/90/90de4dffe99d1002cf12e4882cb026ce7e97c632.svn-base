// 重叠柱状图+平均线
export const handlingInformationOption = (xData = [], c1Data = [], c2Data = [], c1verage = 0, c2verage = 0) => {
  const c1verageData = Array(c1Data.length).fill(c1verage)
  const c2verageData = Array(c2Data.length).fill(c2verage)
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: 'rgba(0,0,0,0.01)',
          type: 'solid',
          width: 40
        }
      }
    },
    legend: {
      top: '15px',
      left: '20px',
      itemGap: 16,
      itemWidth: 16,
      textStyle: {
        color: '#AEAEAE',
        fontSize: '12'
      }
    },
    grid: {
      left: '50px',
      right: '20px',
      bottom: '50px',
      top: '60px',
      containLabel: false
    },
    dataZoom: [
      {
        id: 'dataZoomX',
        type: 'inside',
        xAxisIndex: [0],
        filterMode: 'filter', // 设定为 'filter' 从而 X 的窗口变化会影响 Y 的范围。
        minSpan: 6,
        maxValueSpan: 6
      },
    ],
    xAxis: [{
      type: 'category',
      offset: 6,
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      },
      axisLabel: {
        fontSize: 11,
        color: '#777'
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: [5, 6],
          dashOffset: 3,
          color: '#EFEFEF',
          width: 1
        },
      },
      data: xData
    }],
    yAxis: [{
      show: true,
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          type: [5, 6],
          dashOffset: 3,
          color: '#EFEFEF',
          width: 1
        },
      },
    }],
    series: [
      {
        name: '处理数',
        type: 'bar',
        stack: 'Ad',
        barMaxWidth: 18,
        itemStyle: {
          color: '#7FAAF2'
        },
        data: c1Data
      },
      {
        name: '预警数',
        type: 'bar',
        stack: 'Ad',
        barMaxWidth: 18,
        itemStyle: {
          color: 'rgba(116, 173, 246, 0.5)'
        },
        data: c2Data
      },
      {
        name: '班级平均预警数',
        type: 'line',
        color: '#30FF91',
        symbol: "line",
        data: c1verageData,
        markLine: {
          data: [{
            type: 'average',
            name: '平均值',
            yAxis: 200,
            label: {
              show: false
            }
          }],
          symbol: 'none',
          lineStyle: {
            color: '#30FF91',
            type: 'solid',
            width: 3,
            shadowColor: 'rgba(177, 255, 206, 0.5)',
            shadowBlur: 8,
            shadowOffsetX: 0,
            shadowOffsetY: 0
          },
        }
      },
      {
        name: '班级平均处理数',
        type: 'line',
        color: 'rgba(255, 229, 0, 1)',
        symbol: "line",
        data: c2verageData,
        markLine: {
          data: [{
            type: 'average',
            name: '平均值',
            yAxis: 100,
            label: {
              show: false
            }
          }],
          symbol: 'none',
          lineStyle: {
            color: 'rgba(255, 229, 0, 1)',
            type: 'solid',
            width: 3,
            shadowColor: 'rgba(255, 219, 144, 0.5)',
            shadowBlur: 8,
            shadowOffsetX: 0,
            shadowOffsetY: 0,
          },
        }
      },
    ]
  }
}

//单柱加平均线
export const singleVerageOption = (xData = [], c1Data = [], c1verage = 0) => {
  const c1verageData = Array(c1Data.length).fill(c1verage)
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: 'rgba(0,0,0,0.01)',
          type: 'solid',
          width: 40
        }
      }
    },
    legend: {
      top: '15px',
      left: '20px',
      itemGap: 16,
      itemWidth: 16,
      textStyle: {
        color: '#AEAEAE',
        fontSize: '12'
      }
    },
    grid: {
      left: '50px',
      right: '20px',
      bottom: '50px',
      top: '60px',
      containLabel: false
    },
    dataZoom: [
      {
        id: 'dataZoomX',
        type: 'inside',
        xAxisIndex: [0],
        filterMode: 'filter', // 设定为 'filter' 从而 X 的窗口变化会影响 Y 的范围。
        minSpan: 6,
        maxValueSpan: 6
      },
    ],
    xAxis: [{
      type: 'category',
      offset: 6,
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      },
      axisLabel: {
        fontSize: 11,
        color: '#777'
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: [5, 6],
          dashOffset: 3,
          color: '#EFEFEF',
          width: 1
        },
      },
      data: xData
    }],
    yAxis: [{
      show: true,
      type: 'value',
      splitLine: {
        show: true,
        lineStyle: {
          type: [5, 6],
          dashOffset: 3,
          color: '#EFEFEF',
          width: 1
        },
      },
    }],
    series: [
      {
        name: '处理数',
        type: 'bar',
        stack: 'Ad',
        barMaxWidth: 18,
        itemStyle: {
          color: '#959FF7'
        },
        data: c1Data
      },
      {
        name: '班级平均处理数',
        type: 'line',
        color: 'rgba(255, 229, 0, 1)',
        symbol: "line",
        data: c1verageData,
        markLine: {
          data: [{
            type: 'average',
            name: '平均值',
            yAxis: c1verage,
            label: {
              show: false
            }
          }],
          symbol: 'none',
          lineStyle: {
            color: 'rgba(255, 229, 0, 1)',
            type: 'solid',
            width: 3,
            shadowColor: 'rgba(255, 219, 144, 0.5)',
            shadowBlur: 8,
            shadowOffsetX: 0,
            shadowOffsetY: 0,
          },
        }
      },
    ]
  }
}
export const brokenLineOption = (xData = [], c1Data = [], c2Data = [], c3Data = [], c4Data = []) => {
  return {
    grid: {
      left: '50px',
      right: '20px',
      top: '60px',
      bottom: '50px',
    },
    legend: {
      top: '15px',
      left: '20px',
      itemGap: 20,
      itemHeight: 6,
      itemWidth: 14,
      textStyle: {
        color: '#556677'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
        lineStyle: {
          color: '#dddddd',
          type: [9, 10],
          dashOffset: 5,
        },
      },
    },
    dataZoom: [
      {
        id: 'dataZoomX',
        type: 'inside',
        xAxisIndex: [0],
        filterMode: 'filter', // 设定为 'filter' 从而 X 的窗口变化会影响 Y 的范围。
        minSpan: 6,
        maxValueSpan: 6
      },
    ],
    xAxis: {
      type: 'category',
      axisLine: {
        show: false,

      },
      offset: 10,
      axisTick: {
        show: false,
      },
      axisLabel: {
        fontSize: 11,
        color: '#777777'
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: [5, 6],
          dashOffset: 3,
          color: '#EFEFEF',
          width: 1
        },
      },
      data: xData,
      boundaryGap: true
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: [5, 6],
          dashOffset: 3,
          color: '#EFEFEF',
          width: 1
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        fontSize: 13,
        color: '#777777'
      },
    },
    series: [{
      name: '通信工程',
      type: 'line',
      data: c1Data,
      symbolSize: 0,
      yAxisIndex: 0,
      smooth: true,
      showSymbol: true,
      lineStyle: {
        width: 3,
        shadowColor: '#FFEEBB',
        shadowBlur: 8,
        shadowOffsetY: 12
      },
      itemStyle: {
        color: '#F7D182',
        borderColor: '#F7D182'
      }
    }, {
      name: '信息工程',
      type: 'line',
      data: c2Data,
      symbolSize: 0,
      yAxisIndex: 0,
      smooth: true,
      showSymbol: true,
      lineStyle: {
        width: 3,
        shadowColor: '#CBBBFF',
        shadowBlur: 8,
        shadowOffsetY: 12
      },
      itemStyle: {
        color: '#AC8AEB',
        borderColor: '#AC8AEB'
      }
    }, {
      name: '经济管理',
      type: 'line',
      data: c3Data,
      symbolSize: 0,
      yAxisIndex: 0,
      smooth: true,
      showSymbol: true,
      lineStyle: {
        width: 3,
        shadowColor: '#FFBBBB',
        shadowBlur: 8,
        shadowOffsetY: 12
      },
      itemStyle: {
        color: '#E78D89',
        borderColor: '#E78D89'
      }
    },
    {
      name: '军士教育',
      type: 'line',
      data: c4Data,
      symbolSize: 0,
      yAxisIndex: 0,
      smooth: true,
      showSymbol: true,
      lineStyle: {
        width: 3,
        shadowColor: '#BBFFCD',
        shadowBlur: 8,
        shadowOffsetY: 12
      },
      itemStyle: {
        color: '#B4E3AE',
        borderColor: '#B4E3AE'
      }
    }
    ]
  }
}