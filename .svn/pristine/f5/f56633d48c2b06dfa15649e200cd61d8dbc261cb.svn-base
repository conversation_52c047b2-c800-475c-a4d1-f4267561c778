import vue from '@vitejs/plugin-vue'
import { configCompressPlugin } from './compress'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { visualizer } from "rollup-plugin-visualizer"
import removeConsole from "vite-plugin-remove-console"
import { VantResolver } from '@vant/auto-import-resolver'
import vueDevTools from 'vite-plugin-vue-devtools'
import vueJsx from '@vitejs/plugin-vue-jsx'
import tailwindcss from '@tailwindcss/vite'
import VueRouter from 'unplugin-vue-router/vite'
import VueLayouts from 'vite-plugin-vue-layouts-next'
import { viteBuildInfo } from './info'
import { createViteVConsole } from './vconsole'

export function getPluginsList(VITE_COMPRESSION, VITE_APP_VCONSOLE) {
  const lifecycle = process.env.npm_lifecycle_event
  return [
    VueRouter({
      routesFolder: [{
        src: 'src/views',
        extensions: ['.vue'],
        exclude: ['**/components/**'],
      }],
      dts: false
    }),
    VueLayouts({
      layoutsDirs: 'src/**/layout',
      pagesDirs: [],
      defaultLayout: '',
    }),
    vueDevTools(),
    tailwindcss(),
    vue(),
    vueJsx(),
    viteBuildInfo(),
    // https://github.com/antfu/unplugin-vue-components 自动导入组件
    Components({
      extensions: ['vue'],
      resolvers: [VantResolver({ importStyle: true })],
      include: [/\.vue$/, /\.vue\?vue/],
      dts: false,
    }),
    // https://github.com/antfu/unplugin-auto-import 自动导入 API
    AutoImport({
      include: [
        /\.[tj]sx?$/,
        /\.vue$/,
        /\.vue\?vue/,
      ],
      imports: [
        'vue',
        'vue-router'
      ],
      dts: false,
      resolvers: [VantResolver()],
      // eslint globals Docs - https://eslint.org/docs/user-guide/configuring/language-options#specifying-globals
      eslintrc: {
        enabled: false, // Default `false`
        // provide path ending with `.mjs` or `.cjs` to generate the file with the respective format
        filepath: './.eslintrc-auto-import.json', // Default `./.eslintrc-auto-import.json`
        globalsPropValue: true, // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
      },
    }),
    // 开发环境vconsole
    createViteVConsole(VITE_APP_VCONSOLE),
    // 压缩
    configCompressPlugin(VITE_COMPRESSION),
    // 线上环境删除console
    removeConsole({ external: [] }),
    // 打包分析
    lifecycle === "report"
      ? visualizer({ open: true, brotliSize: true, filename: "report.html" })
      : null
  ]
}
