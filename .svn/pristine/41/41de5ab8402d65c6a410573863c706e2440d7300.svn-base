<template>
  <div class="todo-class bg-[#F0F2F9] h-full w-full flex flex-col">
    <div class="bg-white">
      <div class="relative h-[88px] flex items-center">
        <p
          class="flex-1 h-full box-border text-center pt-[20px] text-[24px] text-[#BFBFBF] font-normal font-pingfang"
          v-for="item in todoTabsOptions"
          :key="item.key"
          :class="{ '!text-[#232528]': activeTab === item.key }"
          @click="onTabClick(item.key)">
          {{ item.label }}
        </p>
        <img
          :src="$getAssetsFile('tabs-active-icon.png')"
          alt=""
          class="w-3 h-1.5 absolute bottom-1.5 translate-x-[-50%] transition-all duration-300"
          :class="todoTabsOptions.find((item) => item.key === activeTab)?.class" />
      </div>
      <p class="bg-[#FFF8F0] p-2.5 text-[#EC8800] text-[22px] font-normal font-pingfang">【温馨提示】以下预警信息基于教务系统产生，最终解释权在教务处。</p>
      <SearchItem v-model:searchParams="searchParams" :options="searchOptions" />
    </div>
    <div class="flex-1 min-h-0 py-[24px]">
      <div class="h-full overflow-y-auto px-[26px]">
        <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div class="flex flex-col gap-y-[30px]">
            <ListItem v-for="item in list" :key="item" btnName="批量处理" :options="listOptions" :data="item" :status="activeTab" @click="onItemClick" @btnClick="onBtnClick">
              <template #title>预警班级：{{ item.bjmc }}</template>
            </ListItem>
          </div>
        </van-list>
      </div>
    </div>
  </div>
</template>

<script setup>
import SearchItem from "@/views/todo/components/search-item.vue";
import ListItem from "./components/list-item.vue";
import { getClassTodoPageAPI } from "@/api/todo";
import { useRouter, useRoute } from "vue-router";

defineOptions({
  name: "TodoClass",
});
definePage({
  name: "TodoClass",
  meta: {
    layout: "index",
    title: "待办",
    navBar: true,
    isAuth: false,
  },
});
const activeTab = ref("0");
const todoTabsOptions = readonly([
  {
    label: "待办",
    key: "0",
    class: "left-[25%]",
  },
  {
    label: "已办",
    key: "1",
    class: "left-[75%]",
  },
]);
const onTabClick = (key) => {
  searchParams.bjbh = "";
  searchParams.nj = "";
  activeTab.value = key;
  onRefresh();
};

const searchParams = reactive({
  nj: "", //年级
  bjbh: "", //班级编号
  yjbh: "", //预警批次
});
const searchOptions = ref([
  {
    key: "nj", //获取选择数据源
    label: "年级", //显示名称
    value: "nj", //获取数据源
    type: "select", //类型
    placeholder: "全部年级", //placeholder
  },
  {
    key: "bj", //获取选择数据源
    label: "班级", //显示名称
    value: "bjbh", //获取数据源
    type: "select", //类型
    placeholder: "全部班级", //placeholder
  },
  {
    key: "yjpc",
    label: "预警批次",
    value: "yjbh",
    type: "select",
    placeholder: "预警批次",
  },
]);

const listOptions = readonly([
  {
    label: "预警人数",
    value: "yjrs",
  },
  {
    label: "预警课程数",
    value: "yjkcs",
  },
]);

const pageCurrent = ref(1);
const list = ref([]);
const loading = ref(false);
const finished = ref(false);
const onLoad = () => {
  getClassTodoPageAPI({
    pageCurrent: pageCurrent.value,
    pageSize: 10,
    clzt: activeTab.value, //0-待办 1-已办
    ...searchParams,
  }).then((res) => {
    const data = res.records;
    if (pageCurrent.value === 1) {
      list.value = data;
    } else {
      list.value.push(...data);
    }
    // 加载状态结束
    loading.value = false;
    if (res.total <= list.value.length) {
      finished.value = true;
    } else {
      pageCurrent.value++;
    }
  });
};
const onRefresh = () => {
  pageCurrent.value = 1;
  list.value = [];
  // 清空列表数据
  finished.value = false;
  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  onLoad();
};

const router = useRouter();
const onItemClick = (item) => {
  router.push({
    path: "/todo/todo-student",
    query: {
      yjbh: item.yjbh,
    },
  });
};
const onBtnClick = (row) => {
  router.push({
    path: "/todo/batch",
    query: {
      nj: row.nj,
      yjbh: row.yjbh,
      yxbh: row.yxbh,
      zybh: row.zybh,
      bjbh: row.bjbh,
      kcbh: row.kcbh, // 课程编号 批量处理提交时需要字段
      sfxx: "0", // 是否选修 批量处理提交时需要字段
      type: "handle",
    },
  });
};
watch([activeTab, searchParams], (_val, _val2) => {
  onRefresh();
});
</script>

<style lang="scss" scoped></style>
