<template>
  <div class="bg-[#f0f2f9] h-full w-full flex flex-col">
    <div class="bg-white">
      <p class="bg-[#FFF8F0] p-2.5 text-[#EC8800] text-[24px] font-normal font-pingfang">【温馨提示】学生毕业的最终解释权归四川邮电职业技术学院所有</p>
      <SearchItem v-model:searchParams="searchParams" :options="searchOptions" />
    </div>
    <div class="flex-1 min-h-0 px-[26px] overflow-y-auto my-3.75">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
        <div class="flex flex-col gap-y-[30px]">
          <ListItem v-for="item in list" :key="item" :isBtn="item.clzt === 0" btnName="批量处理" :options="options" :data="item" @btnClick="onBtnClick(item)" @click="onItemClick(item)">
            <template #title>
              <span>预警班级：{{ item.bjmc }}</span>
            </template>
          </ListItem>
        </div>
      </van-list>
    </div>
  </div>
</template>

<script setup>
import ListItem from "@/views/todo/components/list-item.vue";
import SearchItem from "@/views/todo/components/search-item.vue";
import { getAlarmInstructorClassListAPI } from "@/api/alarm";
defineOptions({
  name: "InstructorClass",
  title: "预警信息-辅导员-班级",
});
definePage({
  name: "InstructorClass",
  meta: {
    layout: "index",
    title: "预警信息",
    navBar: true,
    isAuth: false,
  },
});

const searchParams = ref({
  bjbh: "",
});
const searchOptions = ref([
  {
    key: "bj",
    label: "班级",
    value: "bjbh",
    type: "select",
    placeholder: "全部班级",
  },
]);

const options = ref([
  {
    label: "预警批次",
    value: "yjbh",
  },
  {
    label: "预警时间",
    value: "yjsj",
  },
  {
    label: "预警年级",
    value: "nj",
  },
  {
    label: "预警人数",
    value: "yjrs",
    format: (row) => {
      return `${row.yjrs || 0}人`;
    },
  },
  {
    label: "预警课程数",
    value: "yjkcs",
    format: (row) => {
      return `${row.yjkcs || 0}门`;
    },
  },
]);
const list = ref([]);
const loading = ref(false);
const finished = ref(false);
const pageCurrent = ref(1);
const onLoad = () => {
  getAlarmInstructorClassListAPI({ ...searchParams.value, current: pageCurrent.value, size: 10 }).then((res) => {
    if (pageCurrent.value === 1) {
      list.value = res.records;
    } else {
      list.value.push(...res.records);
    }
    loading.value = false;
    if (res.total <= list.value.length) {
      finished.value = true;
    } else {
      pageCurrent.value++;
    }
  });
};
const onRefresh = () => {
  // 清空列表数据
  finished.value = false;
  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  pageCurrent.value = 1;
  list.value.length = 0;
  onLoad();
};
const router = useRouter();
const onBtnClick = (row) => {
  router.push({
    path: "/todo/batch",
    query: {
      nj: row.nj,
      yjbh: row.yjbh,
      yxbh: row.yxbh,
      zybh: row.zybh,
      bjbh: row.bjbh,
      kcbh: row.kcbh, // 课程编号 批量处理提交时需要字段
      sfxx: "0", // 是否选修 批量处理提交时需要字段
      type: "handle",
    },
  });
};

const onItemClick = (row) => {
  router.push({
    path: "/alarm/instructor/student",
    query: {
      yjbh: row.yjbh,
      nj: row.nj,
      yxbh: row.yxbh,
      zybh: row.zybh,
      bjbh: row.bjbh,
    },
  });
};
watch(
  () => searchParams.value.bjbh,
  () => {
    onRefresh();
  },
);
</script>

<style lang="scss" scoped></style>
