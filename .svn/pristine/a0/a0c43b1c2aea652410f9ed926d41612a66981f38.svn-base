import { mitter } from '@cm/utils'
import { fistPage, loginExpired } from '@/config/website'
import { ROUTER_EVENTS } from '../constants'
import tokenService from '../services/token'

/**
 * 认证相关路由守卫
 */

/**
 * 处理登录过期 - 返回登录页面路径
 * @returns {string} 登录页面路径
 */
export function handleLoginExpired() {
  mitter.emit(ROUTER_EVENTS.LOGIN_EXPIRED)
  return loginExpired
}

/**
 * 检查认证状态的守卫
 * @param {object} to - 目标路由对象
 * @param {object} _from - 来源路由对象
 * @returns {Promise<boolean|string|object>} 守卫执行结果
 */
export async function authGuard(to, _from) {
  try {
    // 获取用户认证状态
    const isAuthenticated = await tokenService.isAuthenticated()

    // 已登录用户访问登录页时重定向到首页
    if (isAuthenticated && to.path === loginExpired) {
      return { path: fistPage.path }
    }

    // 不需要认证的页面直接通过
    if (to.meta.isAuth === false) {
      return true
    }

    // 未登录用户重定向到登录页
    if (!isAuthenticated) {
      return handleLoginExpired()
    }

    // 认证通过，继续执行后续守卫
    return true
  }
  catch (error) {
    console.error('认证检查失败:', error)
    return handleLoginExpired()
  }
}

/**
 * 初始化认证相关事件监听器
 */
export function initAuthListeners() {
  // 监听登录过期事件
  mitter.on(ROUTER_EVENTS.LOGIN_EXPIRED, async () => {
    try {
      await tokenService.clearAll()
      // 直接跳转到登录页
      const router = await import('../index')
      router.default.push(loginExpired)
    }
    catch (error) {
      console.error('处理登录过期失败:', error)
    }
  })
}
