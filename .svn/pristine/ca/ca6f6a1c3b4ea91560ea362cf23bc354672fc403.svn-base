import { execSync } from 'node:child_process'
import process from 'node:process'

/**
 * 图标定义常量
 * @type {object}
 */
const ICONS = {
  ERROR: '❌',
  SUCCESS: '✅',
  WARNING: '⚠️',
  INFO: '📢',
  CLEAN: '🧹',
  PACKAGE: '📦',
}

/**
 * 安全删除文件或目录的辅助函数
 * @param {string} target - 要删除的目标
 * @param {string} description - 描述信息
 */
function safeRemove(target, description) {
  try {
    execSync(`rimraf ${target}`)
    console.log(`\x1B[32m${ICONS.SUCCESS} %s\x1B[0m`, `已删除 ${description}`)
  }
  catch {
    console.log(`${ICONS.WARNING} ${description} 可能不存在或无法删除`)
  }
}

/**
 * 检查并强制使用 pnpm 作为包管理器
 * 如果检测到使用其他包管理器，会清理环境并提示使用 pnpm
 * @returns {Promise<void>} Promise that resolves when check is complete
 */
export async function checkPnpm() {
  const userAgent = process.env.npm_config_user_agent

  // 检查当前环境变量中的 npm_config_user_agent 是否存在且以 'pnpm' 开头
  if (!userAgent?.startsWith('pnpm')) {
    // 如果不是使用 pnpm，则输出红色错误信息
    console.error(`\x1B[31m${ICONS.ERROR} %s\x1B[0m`, '错误：请使用 pnpm 作为唯一的包管理器来安装依赖。')
    console.log(`\n${ICONS.CLEAN} 清理环境中...`)
    // 清理不同包管理器的文件
    const cleanupTargets = [
      { target: 'node_modules', description: 'node_modules 目录' },
      { target: 'package-lock.json', description: 'package-lock.json 文件' },
      { target: 'yarn.lock', description: 'yarn.lock 文件' },
    ]

    cleanupTargets.forEach(({ target, description }) => {
      safeRemove(target, description)
    })

    console.error(`${ICONS.INFO} 请运行: \x1B[36mpnpm install\x1B[0m`)
    // 以错误状态码 1 退出进程，中止安装
    process.exit(1)
  }
  else {
    console.log(`${ICONS.SUCCESS} 使用 pnpm 安装依赖中...`)
  }
}
