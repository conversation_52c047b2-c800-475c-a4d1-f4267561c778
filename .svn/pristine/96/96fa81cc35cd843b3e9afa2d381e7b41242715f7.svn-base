import localforage from 'localforage'
import crypto from './crypto.js' // 导入加密工具

/**
 * 安全存储管理器 - 高级版本
 *
 * 特性:
 * 1. 高级加密保护
 * 2. 内存缓存层
 * 3. 数据过期管理
 * 4. 批量操作支持
 */

// 常量定义
const CIPHER_VERSION = '1.0'
const AVAILABLE_DRIVERS = [
  localforage.INDEXEDDB,
  localforage.WEBSQL,
  localforage.LOCALSTORAGE,
]
const STORAGE_PREFIX = '[存储管理器]'

// 内部工具函数
const util = {
  isObject(value) {
    return value !== null && typeof value === 'object' && !Array.isArray(value)
  },

  // 安全的JSON解析
  safeJsonParse(str, fallback = null) {
    try {
      return JSON.parse(str)
    }
    catch (error) {
      void error // 明确忽略错误信息
      return fallback
    }
  },

  // 获取当前时间戳
  now() {
    return Date.now()
  },

  // 格式化错误日志
  logError(message, error) {
    console.error(`${STORAGE_PREFIX} ${message}`, error)
  },
}

/**
 * 存储管理器类
 * 提供安全的数据存储、读取和管理功能
 */
class SafeStorageManager {
  /** @type {SafeStorageManager|null} 单例实例 */
  static #instance = null

  /** @type {Map} 内存缓存 */
  #memoryCache = new Map()

  /** @type {boolean} 内存缓存启用状态 */
  #cacheEnabled = true

  /** @type {boolean} 是否已初始化 */
  #initialized = false

  /**
   * 构造函数
   * @param {object} options 配置选项
   * @private
   */
  constructor(options = {}) {
    // 防止直接实例化
    if (SafeStorageManager.#instance) {
      throw new Error(`${STORAGE_PREFIX} 禁止直接实例化，请使用getInstance方法`)
    }

    // 初始化配置
    const {
      driverPriority = AVAILABLE_DRIVERS,
      name = 'cm-app-vite',
      storeName = 'app_data',
      encryption = true,
      enableCache = true,
    } = options

    // 存储配置
    this.#cacheEnabled = enableCache

    // 创建存储实例
    this.store = localforage.createInstance({
      driver: driverPriority,
      name,
      storeName,
    })

    // 加密配置
    this.encryption = encryption

    // 初始化存储系统
    this.ready = this.#initialize()

    // 标记初始化完成
    this.#initialized = true
  }

  /**
   * 初始化存储系统
   * @private
   */
  async #initialize() {
    try {
      // 初始化存储引擎
      await localforage.setDriver(AVAILABLE_DRIVERS)
      await localforage.ready()

      // 预热缓存
      if (this.#cacheEnabled) {
        await this.#warmupCache()
      }

      return true
    }
    catch (error) {
      util.logError('初始化失败:', error)
      return false
    }
  }

  /**
   * 预热内存缓存
   * @private
   */
  async #warmupCache() {
    try {
      // 限制只缓存常用和小型数据
      const keys = await this.store.keys()
      const keysToCache = keys.slice(0, 20) // 限制缓存条目数
      const now = util.now()

      for (const key of keysToCache) {
        const value = await this.store.getItem(key)
        if (value && (!value.expire || value.expire > now)) {
          this.#cacheItem(key, value.data, false)
        }
      }
    }
    catch (error) {
      util.logError('缓存预热失败:', error)
    }
  }

  /**
   * 将项目添加到缓存
   * @param {string} key 键名
   * @param {any} value 值
   * @param {boolean} isDecrypted 是否已解密
   * @private
   */
  #cacheItem(key, value, isDecrypted = true) {
    if (this.#cacheEnabled) {
      this.#memoryCache.set(key, {
        value,
        timestamp: util.now(),
        isDecrypted,
      })
    }
  }

  /**
   * 清理过期的存储项
   * @returns {Promise<number>} 清理的项目数量
   */
  async cleanExpiredItems() {
    try {
      const now = util.now()
      const keys = await this.store.keys()
      let cleanedCount = 0

      for (const key of keys) {
        const item = await this.store.getItem(key)
        if (item && item.expire && item.expire < now) {
          // 移除过期项
          await this.store.removeItem(key)
          // 同时清除缓存
          this.#memoryCache.delete(key)
          cleanedCount++
        }
      }

      return cleanedCount
    }
    catch (error) {
      util.logError('清理过期数据失败:', error)
      return 0
    }
  }

  /**
   * 加密数据
   * @param {any} data 要加密的数据
   * @returns {string} 加密后的数据
   * @private
   */
  #encrypt(data) {
    if (!this.encryption)
      return data

    try {
      // 添加加密版本和时间戳以便将来升级加密算法
      const payload = {
        v: CIPHER_VERSION,
        t: util.now(),
        d: data,
      }
      const jsonStr = JSON.stringify(payload)
      return crypto.encrypt(jsonStr)
    }
    catch (error) {
      util.logError('加密失败:', error)
      return data
    }
  }

  /**
   * 解密数据
   * @param {string} encrypted 加密的数据
   * @returns {any} 解密后的数据
   * @private
   */
  #decrypt(encrypted) {
    if (!this.encryption || typeof encrypted !== 'string')
      return encrypted

    try {
      const decrypted = crypto.decrypt(encrypted)
      const parsed = util.safeJsonParse(decrypted)

      // 验证解密后的数据格式
      if (parsed && typeof parsed === 'object' && 'v' in parsed && 'd' in parsed) {
        return parsed.d
      }

      // 旧版本兼容处理
      return util.safeJsonParse(decrypted, encrypted)
    }
    catch (error) {
      util.logError('解密失败:', error)
      return encrypted
    }
  }

  /**
   * 确保存储系统已初始化
   * @private
   */
  async #ensureReady() {
    if (!this.#initialized) {
      await this.ready
    }
  }

  /**
   * 获取单例实例
   * @param {object} options 配置选项
   * @returns {SafeStorageManager} 存储管理器实例
   */
  static getInstance(options) {
    if (!this.#instance) {
      this.#instance = new SafeStorageManager(options)
    }
    return this.#instance
  }

  /**
   * 从缓存获取解密数据
   * @param {string} key 键名
   * @returns {any|null} 缓存的数据或null
   * @private
   */
  #getFromCache(key) {
    if (!this.#cacheEnabled || !this.#memoryCache.has(key)) {
      return null
    }

    const cached = this.#memoryCache.get(key)
    let value = cached.value

    // 只在需要时解密
    if (!cached.isDecrypted) {
      value = this.#decrypt(value)
      // 更新缓存中的解密状态
      this.#cacheItem(key, value, true)
    }

    return value
  }

  /**
   * 获取存储内容（带过期校验）
   * @param {string} key 存储键名
   * @param {any} defaultValue 默认值（可传入函数生成）
   * @param {object|boolean} options 选项或是否自动保存默认值
   * @param {boolean} [options.autoSave] 是否自动保存默认值
   * @param {boolean} [options.bypassCache] 是否绕过缓存
   * @returns {Promise<any>} 存储内容
   */
  async get(key, defaultValue, options = {}) {
    try {
      await this.#ensureReady()

      const { autoSave = false, bypassCache = false }
        = typeof options === 'boolean' ? { autoSave: options } : options

      // 检查内存缓存
      if (!bypassCache) {
        const cachedValue = this.#getFromCache(key)
        if (cachedValue !== null) {
          return cachedValue
        }
      }

      // 从存储中获取
      const result = await this.store.getItem(key)

      // 处理不存在值的情况
      if (result === null) {
        return this.#handleDefaultValue(key, defaultValue, autoSave)
      }

      // 处理过期数据
      if (result.expire && result.expire < util.now()) {
        await this.remove(key)
        return this.#handleDefaultValue(key, defaultValue)
      }

      // 解密数据
      const decryptedData = this.#decrypt(result.data)

      // 更新缓存
      this.#cacheItem(key, decryptedData)

      return decryptedData
    }
    catch (error) {
      util.logError(`获取键 "${key}" 失败:`, error)
      return this.#handleDefaultValue(key, defaultValue)
    }
  }

  /**
   * 处理默认值逻辑
   * @param {string} key 键名
   * @param {any} defaultValue 默认值
   * @param {boolean} autoSave 是否自动保存
   * @returns {any} 处理后的值
   * @private
   */
  async #handleDefaultValue(key, defaultValue, autoSave = false) {
    if (defaultValue === undefined) {
      return null
    }

    const value = typeof defaultValue === 'function'
      ? defaultValue()
      : defaultValue

    if (autoSave) {
      await this.set(key, value)
    }

    return value
  }

  /**
   * 存储数据
   * @param {string} key 存储键名
   * @param {any} value 存储值
   * @param {number|null} expire 过期时间（秒），null表示永不过期
   * @returns {Promise<any>} 存储的值
   */
  async set(key, value, expire = null) {
    try {
      await this.#ensureReady()

      // 加密数据
      const encryptedValue = this.#encrypt(value)

      const saveData = {
        data: encryptedValue,
        expire: expire ? util.now() + expire * 1000 : undefined,
        updated: util.now(),
      }

      // 保存到本地存储
      await this.store.setItem(key, saveData)

      // 更新内存缓存
      this.#cacheItem(key, value)

      return value
    }
    catch (error) {
      util.logError(`设置键 "${key}" 失败:`, error)
      return null
    }
  }

  /**
   * 移除指定键的数据
   * @param {string} key 要移除的键名
   * @returns {Promise<boolean>} 操作是否成功
   */
  async remove(key) {
    try {
      await this.#ensureReady()

      // 从存储中移除
      await this.store.removeItem(key)

      // 清除缓存
      this.#memoryCache.delete(key)

      return true
    }
    catch (error) {
      util.logError(`移除键 "${key}" 失败:`, error)
      return false
    }
  }

  /**
   * 清空所有存储数据
   * @returns {Promise<boolean>} 操作是否成功
   */
  async clear() {
    try {
      await this.#ensureReady()

      // 清空存储
      await this.store.clear()

      // 清空缓存
      this.#memoryCache.clear()

      return true
    }
    catch (error) {
      util.logError('清空存储失败:', error)
      return false
    }
  }

  /**
   * 获取所有存储键名
   * @returns {Promise<Array<string>>} 键名列表
   */
  async keys() {
    try {
      await this.#ensureReady()
      return await this.store.keys()
    }
    catch (error) {
      util.logError('获取键列表失败:', error)
      return []
    }
  }

  /**
   * 遍历所有存储项
   * @param {Function} iterator 迭代器函数，接收参数(value, key, index)
   * @returns {Promise<void>}
   */
  async iterate(iterator) {
    try {
      await this.#ensureReady()
      const now = util.now()

      await this.store.iterate((value, key, index) => {
        // 检查是否过期
        if (value?.expire && value.expire < now) {
          this.remove(key)
          return
        }

        // 解密数据后传给迭代器
        const decryptedData = this.#decrypt(value?.data)
        iterator(decryptedData, key, index)
      })
    }
    catch (error) {
      util.logError('遍历数据失败:', error)
    }
  }

  /**
   * 批量获取多个键的值
   * @param {Array<string>} keys 键名数组
   * @returns {Promise<object>} 键值对象
   */
  async getBatch(keys) {
    if (!Array.isArray(keys)) {
      return {}
    }

    const result = {}
    await Promise.all(keys.map(async (key) => {
      result[key] = await this.get(key)
    }))

    return result
  }

  /**
   * 批量设置多个键值
   * @param {object} entries 键值对象
   * @param {number|null} expire 过期时间（秒）
   * @returns {Promise<boolean>} 操作是否成功
   */
  async setBatch(entries, expire = null) {
    if (!util.isObject(entries)) {
      return false
    }

    try {
      await Promise.all(
        Object.entries(entries).map(([key, value]) =>
          this.set(key, value, expire),
        ),
      )
      return true
    }
    catch (error) {
      util.logError('批量设置失败:', error)
      return false
    }
  }

  /**
   * 控制缓存功能
   * @param {boolean} enabled 是否启用缓存
   */
  setCacheEnabled(enabled) {
    this.#cacheEnabled = !!enabled

    // 如果禁用缓存，清空现有缓存
    if (!enabled) {
      this.#memoryCache.clear()
    }
  }

  /**
   * 检查键是否存在且未过期
   * @param {string} key 键名
   * @returns {Promise<boolean>} 是否存在且未过期
   */
  async has(key) {
    try {
      await this.#ensureReady()

      // 先检查缓存
      if (this.#cacheEnabled && this.#memoryCache.has(key)) {
        return true
      }

      // 检查存储
      const item = await this.store.getItem(key)

      if (!item) {
        return false
      }

      // 检查是否过期
      if (item.expire && item.expire < util.now()) {
        // 自动清理过期项
        this.remove(key)
        return false
      }

      return true
    }
    catch (error) {
      util.logError(`检查键 "${key}" 失败:`, error)
      return false
    }
  }

  /**
   * 获取存储项的元信息
   * @param {string} key 键名
   * @returns {Promise<object | null>} 元信息
   */
  async getMetadata(key) {
    try {
      await this.#ensureReady()

      const item = await this.store.getItem(key)

      if (!item) {
        return null
      }

      const now = util.now()
      const isExpired = item.expire && item.expire < now

      // 自动清理过期项
      if (isExpired) {
        this.remove(key)
        return null
      }

      return {
        exists: true,
        updated: item.updated || null,
        expires: item.expire || null,
        remainingTime: item.expire ? Math.max(0, item.expire - now) : null,
      }
    }
    catch (error) {
      util.logError(`获取键 "${key}" 元信息失败:`, error)
      return null
    }
  }
}

/**
 * 创建访问控制代理
 * @param {SafeStorageManager} instance 存储管理器实例
 * @returns {Proxy} 代理包装的实例
 */
function createAccessProxy(instance) {
  // 保存方法引用
  const methods = {}

  // 只允许访问这些公共方法和属性
  const allowedProps = [
    'get',
    'set',
    'remove',
    'clear',
    'keys',
    'iterate',
    'getBatch',
    'setBatch',
    'has',
    'getMetadata',
    'setCacheEnabled',
    'cleanExpiredItems', // 添加清理过期项方法
  ]

  // 保存方法
  allowedProps.forEach((prop) => {
    if (typeof instance[prop] === 'function') {
      methods[prop] = instance[prop].bind(instance)
    }
  })

  // 创建代理
  return new Proxy(instance, {
    get(target, prop) {
      // 允许访问指定的公共方法
      if (allowedProps.includes(prop)) {
        return typeof methods[prop] === 'function'
          ? methods[prop]
          : target[prop]
      }

      // 阻止访问私有属性和方法
      if (typeof prop === 'string' && (
        prop.startsWith('#')
        || prop.startsWith('_')
      )) {
        console.warn(`${STORAGE_PREFIX} 阻止访问私有属性: ${String(prop)}`)
        return undefined
      }

      return target[prop]
    },

    set(target, prop, value) {
      // 阻止修改实例的公共方法
      if (allowedProps.includes(prop)) {
        console.error(`${STORAGE_PREFIX} 不允许修改方法: ${String(prop)}`)
        return true
      }

      // 阻止修改私有属性
      if (typeof prop === 'string' && (
        prop.startsWith('#')
        || prop.startsWith('_')
      )) {
        console.error(`${STORAGE_PREFIX} 不允许修改私有属性: ${String(prop)}`)
        return true
      }

      target[prop] = value
      return true
    },

    deleteProperty(target, prop) {
      console.error(`${STORAGE_PREFIX} 不允许删除属性: ${String(prop)}`)
      return false
    },
  })
}

/**
 * 导出单例实例
 * @type {SafeStorageManager}
 */
export default createAccessProxy(SafeStorageManager.getInstance({
  driverPriority: AVAILABLE_DRIVERS,
  name: 'cm-app-vite',
  storeName: 'app_data',
  encryption: true, // 启用加密
  enableCache: false, // 启用内存缓存
}))
