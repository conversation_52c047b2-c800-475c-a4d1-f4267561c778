<template>
  <div
    class="ssw-early h-full w-full">
    <!-- <LeadershipAdminItem /> -->
    <!-- <CollegeAdminItem /> -->
    <InstructorItem />
  </div>

</template>

<script setup>
import LeadershipAdminItem from './components/leadership-admin-item.vue'
import CollegeAdminItem from './components/college-admin-item.vue'
import InstructorItem from './components/instructor-item.vue'
defineOptions({
  name: 'StudentBoard',
})
definePage({
  name: 'StudentBoard',
  meta: {
    layout: 'index',
    title: '学情看板',
    navBar: true,
    isAuth: false,
  }
})
</script>

<style lang="scss" scoped>
.ssw-early {
  ::v-deep(.card-item-hander) {
    &::after {
      content: "";
      width: 8px;
      height: 32px;
      background: var(--line-color);
      border-radius: 4px;
      position: absolute;
      left: 30px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
</style>