<template>
  <div class="bg-[#f0f2f9] h-full w-full flex flex-col">
    <div class="bg-white">
      <p class="bg-[#FFF8F0] p-2.5 text-[#EC8800] text-[24px] font-normal font-pingfang">【温馨提示】学生毕业的最终解释权归四川邮电职业技术学院所有</p>
      <SearchItem v-model:searchParams="searchParams" :options="searchOptions" />
    </div>
    <div class="flex-1 min-h-0 py-[24px]">
      <div class="h-full overflow-y-auto px-[26px]">
        <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div class="flex flex-col gap-y-[30px]">
            <ListItem v-for="item in list" :key="item" btnName="查看处理" :options="listOptions">
              <template #title>
                <span>预警班级：{{ item }}</span>
              </template>
            </ListItem>
          </div>
        </van-list>
      </div>
    </div>
  </div>
</template>

<script setup>
import ListItem from "@/views/todo/components/list-item.vue";
import SearchItem from "@/views/todo/components/search-item.vue";
defineOptions({
  name: "LeaderCollegeMajor",
  title: "校领导-院系专业",
});
definePage({
  name: "LeaderCollegeMajor",
  meta: {
    layout: "index",
    title: "预警信息",
    navBar: true,
    isAuth: false,
  },
});

const searchParams = ref({
  grade: "",
  class: "",
});
const searchOptions = ref([
  {
    key: "yx",
    label: "院系",
    value: "yx",
    type: "select",
    placeholder: "全部院系",
  },
  {
    key: "zy",
    label: "专业",
    value: "zy",
    type: "select",
    placeholder: "全部专业",
  },
  {
    key: "nj", //获取选择数据源
    label: "年级",
    value: "grade",
    type: "select",
    placeholder: "全部年级",
  },
]);

const listOptions = ref([
  {
    label: "预警时间",
    value: "value",
  },
  {
    label: "预警专业",
    value: "value",
  },
  {
    label: "预警年级",
    value: "value",
  },
  {
    label: "预警人数",
    value: "value",
  },
  {
    label: "预警课程数",
    value: "value",
  },
]);
const list = ref([]);
const loading = ref(false);
const finished = ref(false);
const onLoad = () => {
  // 异步更新数据
  // setTimeout 仅做示例，真实场景中一般为 ajax 请求
  setTimeout(() => {
    for (let i = 0; i < 10; i++) {
      list.value.push(list.value.length + 1);
    }
    // 加载状态结束
    loading.value = false;
    // 数据全部加载完成
    if (list.value.length >= 40) {
      finished.value = true;
    }
  }, 1000);
};
const onRefresh = () => {
  // 清空列表数据
  finished.value = false;
  // 重新加载数据
  // 将 loading 设置为 true，表示处于加载状态
  loading.value = true;
  onLoad();
};
</script>

<style lang="scss" scoped></style>
