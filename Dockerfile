# =============================================================================
# Docker 多阶段构建配置文件
# 
# 这个 Dockerfile 使用多阶段构建来优化最终镜像的大小和性能：
# 第一阶段：使用 Node.js 环境构建 Vue 应用
# 第二阶段：使用轻量级 Nginx 环境运行静态文件服务
# 
# 多阶段构建的优势：
# 1. 最终镜像只包含运行时需要的文件，体积更小
# 2. 构建工具和依赖不会包含在生产镜像中，更安全
# 3. 构建过程可复现，环境一致性好
# =============================================================================

# -----------------------------------------------------------------------------
# 第一阶段：构建阶段 (Build Stage)
# -----------------------------------------------------------------------------
# 使用官方 Node.js 镜像作为构建环境
# 选择 Alpine Linux 版本：体积小、安全性高、启动快
# 使用最新版本获得最新的性能提升和功能特性
FROM node:alpine AS builder

# 设置工作目录
# Docker 容器内的所有后续操作都在这个目录下进行
WORKDIR /app

# 启用 Corepack 来管理包管理器
# Corepack 是 Node.js 16+ 内置的包管理器管理工具，可以自动安装和使用项目指定的 pnpm 版本
RUN corepack enable

# 复制包管理器配置文件
# 先复制这些文件可以利用 Docker 的层缓存机制：
# 当依赖没有变化时，不需要重新下载依赖，大大加快构建速度
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./

# 如果存在 .npmrc 文件，也需要复制（用于 pnpm 配置）
COPY .npmrc* ./

# 复制 packages 目录（monorepo 工作区结构）
# 这个项目使用了 pnpm 工作区，需要复制 packages 目录来正确解析依赖
# preinstall 钩子现在使用 @cm/scripts 包中的 check-pnpm 命令
COPY packages/ ./packages/

# 安装项目依赖
# --frozen-lockfile：确保安装的依赖版本与 lock 文件完全一致，避免版本差异
# --prefer-offline：优先使用本地缓存，加快安装速度
RUN pnpm install --frozen-lockfile --prefer-offline

# 复制源代码和配置文件
# 这一步放在依赖安装之后，可以最大化利用 Docker 缓存
COPY . .

# 构建生产版本
# 执行构建命令，生成优化后的静态文件
RUN pnpm run build:production

# -----------------------------------------------------------------------------
# 第二阶段：运行阶段 (Runtime Stage)
# -----------------------------------------------------------------------------
# 使用官方 Nginx 镜像作为 Web 服务器
# Alpine 版本：轻量级，最终镜像只有几十MB
FROM nginx:alpine AS runtime

# 创建非特权用户来运行 Nginx（安全最佳实践）
# 避免以 root 用户运行服务，降低安全风险
RUN addgroup -g 1001 -S nodejs \
  && adduser -S nextjs -u 1001

# 从构建阶段复制构建产物到 Nginx 默认目录
# --from=builder：指定从第一阶段复制文件
# /app/dist：构建阶段的输出目录
# /usr/share/nginx/html：Nginx 默认的静态文件目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制自定义 Nginx 配置文件
# 这个配置文件将包含静态资源服务、API 代理、缓存等配置
COPY nginx.conf /etc/nginx/nginx.conf

# 修改文件所有权为非特权用户
RUN chown -R nextjs:nodejs /usr/share/nginx/html \
  && chown -R nextjs:nodejs /var/cache/nginx \
  && chown -R nextjs:nodejs /var/log/nginx \
  && chown -R nextjs:nodejs /etc/nginx/conf.d \
  && touch /var/run/nginx.pid \
  && chown -R nextjs:nodejs /var/run/nginx.pid

# 切换到非特权用户
USER nextjs

# 暴露端口
# 9977：HTTP 服务端口
EXPOSE 9977

# 添加健康检查
# Docker 会定期执行这个命令来检查容器是否正常运行
# --interval=30s：每30秒检查一次
# --timeout=3s：超时时间3秒
# --start-period=5s：容器启动后5秒开始检查
# --retries=3：失败重试3次
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:9977/ || exit 1

# 启动 Nginx
# -g "daemon off;"：以前台模式运行，这样 Docker 可以正确管理进程
CMD ["nginx", "-g", "daemon off;"]

# =============================================================================
# 构建和运行命令示例：
# 
# 构建镜像：
# docker build -t ssw-app .
# 
# 运行容器：
# docker run -d -p 9977:9977 --name vue-app ssw-app
# 
# 访问应用：
# http://localhost:9977
# ============================================================================= 